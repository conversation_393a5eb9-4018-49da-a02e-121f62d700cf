{"timestamp": "2025-06-07T09:39:07.745650", "overall_status": "CRITICAL", "components": {"dependencies": {"status": "PASS", "missing_packages": []}, "api_keys": {"status": "PASS", "usdt_balance": 10.53030313, "connection": "SUCCESS"}, "files": {"status": "PASS", "file_status": {"live_trading_system.py": "PRESENT", "trading_webapp.py": "PRESENT", "BinanceAPI_2.txt": "PRESENT", "live_trading.log": "PRESENT", "live_trading_state.json": "MISSING", "templates/dashboard.html": "PRESENT"}}, "web_app": {"status": "FAIL", "dashboard": "FAIL", "api_endpoints": {"/api/status": "FAIL", "/api/trades": "FAIL", "/api/equity_curve": "FAIL"}}, "trading_system": {"status": "FAIL", "error": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F9BA6BA860>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "logs": {"status": "PASS", "total_lines": 140, "recent_errors": 0, "recent_warnings": 0}}, "issues": ["Web dashboard not accessible", "Some API endpoints not working", "Trading system check failed"], "recommendations": ["Start web app with: python trading_webapp.py"]}