#!/usr/bin/env python3
"""
Test the corrected training system to verify fixes
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class TestCorrectedTraining:
    """Test the corrected training logic"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for testing")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def get_test_data(self, days: int = 3) -> pd.DataFrame:
        """Get test data"""
        if not self.exchange:
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} days of test data...")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1h', since=since, limit=days * 24)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            print(f"✅ Collected {len(df)} candles")
            return df
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return pd.DataFrame()
    
    def test_corrected_label_generation(self, df: pd.DataFrame) -> Dict:
        """Test the corrected label generation"""
        print("\n🔧 TESTING CORRECTED LABEL GENERATION")
        print("=" * 50)
        
        labels = []
        details = []
        sequence_length = 24
        
        for i in range(sequence_length, len(df)):
            current_price = df.iloc[i]['close']
            
            # CORRECTED: Relaxed grid proximity (0.1% instead of 0.01%)
            grid_size = current_price * 0.0025
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity = abs(current_price - grid_level) / grid_level
            
            if grid_proximity > 0.001:  # FIXED: 0.1% threshold
                label = 2  # HOLD
                reason = f"Too far from grid ({grid_proximity:.6f})"
            else:
                # CORRECTED: Multi-hour lookhead
                best_price_change = 0
                hours_ahead = 0
                
                for j in range(1, min(7, len(df) - i)):  # Look ahead up to 6 hours
                    future_price = df.iloc[i + j]['close']
                    price_change = (future_price - current_price) / current_price
                    
                    if abs(price_change) > abs(best_price_change):
                        best_price_change = price_change
                        hours_ahead = j
                    
                    if abs(price_change) >= 0.0015:  # FIXED: 0.15% threshold
                        break
                
                if best_price_change >= 0.0015:  # +0.15% profit -> BUY
                    label = 0
                    reason = f"BUY: +{best_price_change:.4f} in {hours_ahead}h"
                elif best_price_change <= -0.0015:  # -0.15% profit -> SELL
                    label = 1
                    reason = f"SELL: {best_price_change:.4f} in {hours_ahead}h"
                else:
                    label = 2  # HOLD
                    reason = f"No significant movement ({best_price_change:.4f})"
            
            labels.append(label)
            details.append({
                'price': current_price,
                'grid_proximity': grid_proximity,
                'label': label,
                'reason': reason
            })
        
        # Analyze results
        label_counts = {0: 0, 1: 0, 2: 0}
        for label in labels:
            label_counts[label] += 1
        
        total = len(labels)
        
        return {
            'total_sequences': total,
            'buy_labels': label_counts[0],
            'sell_labels': label_counts[1],
            'hold_labels': label_counts[2],
            'buy_pct': (label_counts[0] / total * 100) if total > 0 else 0,
            'sell_pct': (label_counts[1] / total * 100) if total > 0 else 0,
            'hold_pct': (label_counts[2] / total * 100) if total > 0 else 0,
            'trading_signals': label_counts[0] + label_counts[1],
            'trading_pct': ((label_counts[0] + label_counts[1]) / total * 100) if total > 0 else 0,
            'sample_details': details[:10]
        }
    
    def test_corrected_simulation(self, df: pd.DataFrame) -> Dict:
        """Test corrected simulation with perfect predictions"""
        print("\n🔧 TESTING CORRECTED SIMULATION")
        print("=" * 50)
        
        sequence_length = 24
        df_trading = df.iloc[sequence_length:]
        
        # Generate perfect predictions using corrected logic
        perfect_predictions = []
        perfect_confidence = []
        
        for i in range(len(df_trading) - 6):  # Leave room for 6-hour lookhead
            current_price = df_trading.iloc[i]['close']
            
            # Check grid proximity
            grid_size = current_price * 0.0025
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity = abs(current_price - grid_level) / grid_level
            
            if grid_proximity > 0.001:  # FIXED: 0.1% threshold
                perfect_predictions.append(2)  # HOLD
                perfect_confidence.append(0.5)
            else:
                # Look ahead for best movement
                best_change = 0
                for j in range(1, 7):
                    if i + j < len(df_trading):
                        future_price = df_trading.iloc[i + j]['close']
                        change = (future_price - current_price) / current_price
                        if abs(change) > abs(best_change):
                            best_change = change
                
                if best_change >= 0.0015:  # Will profit from BUY
                    perfect_predictions.append(0)  # BUY
                    perfect_confidence.append(0.95)
                elif best_change <= -0.0015:  # Will profit from SELL
                    perfect_predictions.append(1)  # SELL
                    perfect_confidence.append(0.95)
                else:
                    perfect_predictions.append(2)  # HOLD
                    perfect_confidence.append(0.5)
        
        # Simulate trading
        balance = 300.0
        trades = []
        
        for i in range(len(perfect_predictions)):
            if perfect_confidence[i] < 0.8:
                continue
            
            current_price = df_trading.iloc[i]['close']
            
            # Look ahead for actual exit
            profit = 0
            exit_type = "NO_EXIT"
            
            for j in range(1, 7):  # Check next 6 hours
                if i + j >= len(df_trading):
                    break
                
                future_price = df_trading.iloc[i + j]['close']
                
                if perfect_predictions[i] == 0:  # BUY
                    price_change = (future_price - current_price) / current_price
                    if price_change >= 0.0015:  # +0.15% take profit
                        profit = 20.0
                        exit_type = "TP"
                        break
                    elif price_change <= -0.00075:  # -0.075% stop loss
                        profit = -10.0
                        exit_type = "SL"
                        break
                
                elif perfect_predictions[i] == 1:  # SELL
                    price_change = (current_price - future_price) / current_price
                    if price_change >= 0.0015:  # +0.15% take profit
                        profit = 20.0
                        exit_type = "TP"
                        break
                    elif price_change <= -0.00075:  # -0.075% stop loss
                        profit = -10.0
                        exit_type = "SL"
                        break
            
            if profit != 0:  # Only count trades that exit
                balance += profit
                trades.append({
                    'direction': 'BUY' if perfect_predictions[i] == 0 else 'SELL',
                    'profit': profit,
                    'exit_type': exit_type
                })
        
        # Analyze results
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['profit'] > 0])
        total_profit = sum(t['profit'] for t in trades)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': (winning_trades / total_trades * 100) if total_trades > 0 else 0,
            'total_profit': total_profit,
            'final_balance': balance,
            'return_pct': (balance - 300) / 300 * 100,
            'sample_trades': trades[:5]
        }
    
    def run_test(self):
        """Run comprehensive test"""
        print("🔧 TESTING CORRECTED TRAINING SYSTEM")
        print("=" * 60)
        
        # Get data
        df = self.get_test_data(3)
        if df.empty:
            return
        
        # Test label generation
        label_results = self.test_corrected_label_generation(df)
        
        print(f"\n📊 CORRECTED LABEL RESULTS:")
        print(f"   Total Sequences: {label_results['total_sequences']}")
        print(f"   BUY Labels: {label_results['buy_labels']} ({label_results['buy_pct']:.1f}%)")
        print(f"   SELL Labels: {label_results['sell_labels']} ({label_results['sell_pct']:.1f}%)")
        print(f"   HOLD Labels: {label_results['hold_labels']} ({label_results['hold_pct']:.1f}%)")
        print(f"   Trading Signals: {label_results['trading_signals']} ({label_results['trading_pct']:.1f}%)")
        
        print(f"\n📋 SAMPLE CORRECTED LABELS:")
        for detail in label_results['sample_details'][:5]:
            print(f"   ${detail['price']:,.2f} | Proximity: {detail['grid_proximity']:.6f} | "
                  f"Label: {detail['label']} | {detail['reason']}")
        
        # Test simulation
        sim_results = self.test_corrected_simulation(df)
        
        print(f"\n🎯 CORRECTED SIMULATION RESULTS:")
        print(f"   Total Trades: {sim_results['total_trades']}")
        print(f"   Winning Trades: {sim_results['winning_trades']}")
        print(f"   Losing Trades: {sim_results['losing_trades']}")
        print(f"   Win Rate: {sim_results['win_rate']:.1f}%")
        print(f"   Total Profit: ${sim_results['total_profit']:+.2f}")
        print(f"   Final Balance: ${sim_results['final_balance']:,.2f}")
        print(f"   Return: {sim_results['return_pct']:+.1f}%")
        
        # Evaluation
        print(f"\n✅ IMPROVEMENT ANALYSIS:")
        
        if label_results['trading_pct'] > 50:
            print(f"   ✅ FIXED: More trading signals ({label_results['trading_pct']:.1f}% vs ~35% before)")
        else:
            print(f"   ⚠️  Still low trading signals ({label_results['trading_pct']:.1f}%)")
        
        if sim_results['total_profit'] > 100:
            print(f"   ✅ FIXED: Profitable simulation (${sim_results['total_profit']:+.2f})")
        else:
            print(f"   ❌ Still not profitable enough (${sim_results['total_profit']:+.2f})")
        
        if sim_results['win_rate'] > 60:
            print(f"   ✅ Good win rate ({sim_results['win_rate']:.1f}%)")
        else:
            print(f"   ⚠️  Win rate needs improvement ({sim_results['win_rate']:.1f}%)")

def main():
    """Run test"""
    test = TestCorrectedTraining()
    test.run_test()

if __name__ == "__main__":
    main()
