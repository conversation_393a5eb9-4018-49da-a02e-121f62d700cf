#!/usr/bin/env python3
"""
LIVE TRADING WEB APPLICATION
Real-time monitoring dashboard for the Conservative Elite trading system
"""

from flask import Flask, render_template, jsonify, request
import json
import os
from datetime import datetime, timedelta
from live_trading_system import LiveTradingSystem
import threading
import time

app = Flask(__name__)

# Global trading system instance
trading_system = None

def load_trading_state():
    """Load trading state from file"""
    try:
        if os.path.exists('live_trading_state.json'):
            with open('live_trading_state.json', 'r') as f:
                return json.load(f)
    except:
        pass
    return {}

@app.route('/')
def dashboard():
    """Main dashboard"""
    return render_template('dashboard.html')

@app.route('/api/status')
def get_status():
    """Get current trading status"""
    global trading_system
    
    if trading_system:
        stats = trading_system.get_performance_stats()
        stats['is_running'] = trading_system.is_running
        stats['open_trades_details'] = trading_system.open_trades
        return jsonify(stats)
    else:
        # Load from saved state
        state = load_trading_state()
        state['is_running'] = False
        return jsonify(state)

@app.route('/api/trades')
def get_trades():
    """Get recent trades"""
    global trading_system
    
    if trading_system:
        recent_trades = trading_system.completed_trades[-20:]  # Last 20 trades
        return jsonify(recent_trades)
    else:
        state = load_trading_state()
        recent_trades = state.get('completed_trades', [])[-20:]
        return jsonify(recent_trades)

@app.route('/api/equity_curve')
def get_equity_curve():
    """Get equity curve data"""
    global trading_system
    
    if trading_system:
        return jsonify(trading_system.equity_curve)
    else:
        state = load_trading_state()
        return jsonify(state.get('equity_curve', [300]))

@app.route('/api/start', methods=['POST'])
def start_trading():
    """Start the trading system"""
    global trading_system

    try:
        # Prevent multiple instances
        if trading_system and trading_system.is_running:
            return jsonify({'success': False, 'message': 'Trading system already running'})

        if not trading_system:
            trading_system = LiveTradingSystem(initial_balance=300.0)

        if not trading_system.is_running:
            trading_system.start_trading()
            return jsonify({'success': True, 'message': 'Trading system started'})
        else:
            return jsonify({'success': False, 'message': 'System already running'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/stop', methods=['POST'])
def stop_trading():
    """Stop the trading system"""
    global trading_system
    
    try:
        if trading_system:
            trading_system.stop_trading()
            trading_system.save_state()
        
        return jsonify({'success': True, 'message': 'Trading system stopped'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/settings', methods=['GET', 'POST'])
def trading_settings():
    """Get or update trading settings"""
    global trading_system
    
    if request.method == 'GET':
        if trading_system:
            settings = {
                'grid_spacing': trading_system.grid_spacing,
                'take_profit_pct': trading_system.take_profit_pct,
                'stop_loss_pct': trading_system.stop_loss_pct,
                'risk_amount_base': trading_system.risk_amount_base,
                'grid_proximity_threshold': trading_system.grid_proximity_threshold
            }
            return jsonify(settings)
        else:
            return jsonify({
                'grid_spacing': 0.0025,
                'take_profit_pct': 0.0025,
                'stop_loss_pct': 0.00125,
                'risk_amount_base': 10.0,
                'grid_proximity_threshold': 0.0001
            })
    
    elif request.method == 'POST':
        if trading_system and not trading_system.is_running:
            data = request.json
            trading_system.grid_spacing = data.get('grid_spacing', 0.0025)
            trading_system.take_profit_pct = data.get('take_profit_pct', 0.0025)
            trading_system.stop_loss_pct = data.get('stop_loss_pct', 0.00125)
            trading_system.risk_amount_base = data.get('risk_amount_base', 10.0)
            trading_system.grid_proximity_threshold = data.get('grid_proximity_threshold', 0.0001)
            
            return jsonify({'success': True, 'message': 'Settings updated'})
        else:
            return jsonify({'success': False, 'message': 'Cannot update settings while trading'})

def create_dashboard_template():
    """Create the HTML dashboard template"""
    template_dir = 'templates'
    if not os.path.exists(template_dir):
        os.makedirs(template_dir)
    
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conservative Elite Trading System</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #2a2a2a; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #888; }
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        .controls { text-align: center; margin-bottom: 30px; }
        .btn { padding: 10px 20px; margin: 0 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn-start { background: #4CAF50; color: white; }
        .btn-stop { background: #f44336; color: white; }
        .btn-start:disabled, .btn-stop:disabled { background: #666; cursor: not-allowed; }
        .charts { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .chart-container { background: #2a2a2a; padding: 20px; border-radius: 10px; }
        .trades-table { background: #2a2a2a; padding: 20px; border-radius: 10px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #444; }
        th { background: #333; }
        .status-running { color: #4CAF50; }
        .status-stopped { color: #f44336; }
        .trade-profit { color: #4CAF50; }
        .trade-loss { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Conservative Elite Trading System</h1>
            <p id="status" class="status-stopped">System Stopped</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div id="balance" class="stat-value">$300.00</div>
                <div class="stat-label">Current Balance</div>
            </div>
            <div class="stat-card">
                <div id="profit" class="stat-value">$0.00</div>
                <div class="stat-label">Total Profit</div>
            </div>
            <div class="stat-card">
                <div id="return" class="stat-value">0.0%</div>
                <div class="stat-label">Total Return</div>
            </div>
            <div class="stat-card">
                <div id="trades" class="stat-value">0</div>
                <div class="stat-label">Total Trades</div>
            </div>
            <div class="stat-card">
                <div id="winrate" class="stat-value">0.0%</div>
                <div class="stat-label">Win Rate</div>
            </div>
            <div class="stat-card">
                <div id="opentrades" class="stat-value">0</div>
                <div class="stat-label">Open Trades</div>
            </div>
        </div>

        <div class="controls">
            <button id="startBtn" class="btn btn-start" onclick="startTrading()">Start Trading</button>
            <button id="stopBtn" class="btn btn-stop" onclick="stopTrading()" disabled>Stop Trading</button>
        </div>

        <div class="charts">
            <div class="chart-container">
                <h3>Equity Curve</h3>
                <canvas id="equityChart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Performance Metrics</h3>
                <canvas id="metricsChart"></canvas>
            </div>
        </div>

        <div class="trades-table">
            <h3>Recent Trades</h3>
            <table>
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Direction</th>
                        <th>Entry Price</th>
                        <th>Exit Price</th>
                        <th>P&L</th>
                        <th>Exit Type</th>
                    </tr>
                </thead>
                <tbody id="tradesBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let equityChart, metricsChart;
        
        // Initialize charts
        function initCharts() {
            const equityCtx = document.getElementById('equityChart').getContext('2d');
            equityChart = new Chart(equityCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Balance',
                        data: [],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { labels: { color: '#fff' } } },
                    scales: {
                        x: { ticks: { color: '#fff' }, grid: { color: '#444' } },
                        y: { ticks: { color: '#fff' }, grid: { color: '#444' } }
                    }
                }
            });
        }

        // Update dashboard
        function updateDashboard() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('balance').textContent = `$${data.current_balance?.toFixed(2) || '300.00'}`;
                    
                    const profit = data.total_profit || 0;
                    const profitEl = document.getElementById('profit');
                    profitEl.textContent = `$${profit.toFixed(2)}`;
                    profitEl.className = `stat-value ${profit >= 0 ? 'positive' : 'negative'}`;
                    
                    const returnPct = data.total_return_pct || 0;
                    const returnEl = document.getElementById('return');
                    returnEl.textContent = `${returnPct.toFixed(1)}%`;
                    returnEl.className = `stat-value ${returnPct >= 0 ? 'positive' : 'negative'}`;
                    
                    document.getElementById('trades').textContent = data.total_trades || 0;
                    document.getElementById('winrate').textContent = `${(data.win_rate || 0).toFixed(1)}%`;
                    document.getElementById('opentrades').textContent = data.open_trades || 0;
                    
                    // Update status
                    const statusEl = document.getElementById('status');
                    if (data.is_running) {
                        statusEl.textContent = '🟢 System Running';
                        statusEl.className = 'status-running';
                        document.getElementById('startBtn').disabled = true;
                        document.getElementById('stopBtn').disabled = false;
                    } else {
                        statusEl.textContent = '🔴 System Stopped';
                        statusEl.className = 'status-stopped';
                        document.getElementById('startBtn').disabled = false;
                        document.getElementById('stopBtn').disabled = true;
                    }
                });

            // Update equity curve
            fetch('/api/equity_curve')
                .then(response => response.json())
                .then(data => {
                    if (equityChart && data.length > 0) {
                        equityChart.data.labels = data.map((_, i) => i);
                        equityChart.data.datasets[0].data = data;
                        equityChart.update();
                    }
                });

            // Update trades table
            fetch('/api/trades')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('tradesBody');
                    tbody.innerHTML = '';
                    
                    data.slice(-10).reverse().forEach(trade => {
                        const row = tbody.insertRow();
                        row.innerHTML = `
                            <td>${new Date(trade.exit_time).toLocaleString()}</td>
                            <td>${trade.direction}</td>
                            <td>$${trade.entry_price?.toFixed(2) || 'N/A'}</td>
                            <td>$${trade.exit_price?.toFixed(2) || 'N/A'}</td>
                            <td class="${trade.profit_loss >= 0 ? 'trade-profit' : 'trade-loss'}">
                                $${trade.profit_loss?.toFixed(2) || '0.00'}
                            </td>
                            <td>${trade.exit_type || 'N/A'}</td>
                        `;
                    });
                });
        }

        function startTrading() {
            fetch('/api/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Trading system started!');
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
        }

        function stopTrading() {
            fetch('/api/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Trading system stopped!');
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            updateDashboard();
            setInterval(updateDashboard, 5000); // Update every 5 seconds
        });
    </script>
</body>
</html>'''
    
    with open(os.path.join(template_dir, 'dashboard.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

def main():
    """Main function to run the web app"""
    # Create template
    create_dashboard_template()
    
    print("🚀 STARTING TRADING WEB APPLICATION")
    print("📊 Dashboard available at: http://localhost:5000")
    print("🔧 API endpoints:")
    print("   - GET  /api/status")
    print("   - GET  /api/trades") 
    print("   - GET  /api/equity_curve")
    print("   - POST /api/start")
    print("   - POST /api/stop")
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()
