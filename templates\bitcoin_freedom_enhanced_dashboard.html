<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Freedom Live Trading</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Status Indicators */
        .status-indicators {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-live { background: #00ff88; color: #000; }
        .status-simulation { background: #ff6b35; color: #fff; }
        .status-error { background: #ff4757; color: #fff; }

        /* Bitcoin Price Card */
        .price-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }

        .price-amount {
            font-size: 3.5rem;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .price-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        /* Performance Grid */
        .performance-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .performance-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #00ff88;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-bottom: 5px;
            cursor: help;
        }

        .metric-label[title]:hover {
            opacity: 1;
            color: #00d4aa;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: bold;
        }

        .metric-positive { color: #00ff88; }
        .metric-neutral { color: #ffffff; }

        /* Recent Trades Section */
        .trades-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .trade-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #00ff88;
        }

        .trade-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .trade-type {
            background: #00ff88;
            color: #000;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 10px;
        }

        .trade-status {
            background: #00ff88;
            color: #000;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .trade-profit {
            font-size: 1.2rem;
            font-weight: bold;
            color: #00ff88;
            margin-left: auto;
        }

        .trade-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .trade-column h4 {
            color: #00ff88;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .trade-info {
            font-size: 0.85rem;
            line-height: 1.4;
            opacity: 0.9;
        }

        .financial-summary {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }

        .summary-item {
            font-size: 0.85rem;
        }

        .summary-label {
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .summary-value {
            font-weight: bold;
            color: #00ff88;
        }

        /* Status Cog */
        .status-cog {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .status-cog:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .cog-icon {
            font-size: 18px;
            color: #00ff88;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
        }

        .modal-content {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #fff;
        }

        .modal h2 {
            color: #00ff88;
            margin-bottom: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }

        .status-label {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 1.1rem;
            font-weight: bold;
        }

        /* Auto-refresh indicator */
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .refresh-spinner {
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .performance-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .trade-details {
                grid-template-columns: 1fr;
            }
            
            .summary-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .price-amount {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Status Indicators -->
        <div class="status-indicators">
            <div id="connectionStatus" class="status-badge status-simulation">Simulation Mode</div>
        </div>

        <!-- Bitcoin Price Card -->
        <div class="price-card">
            <div id="bitcoinPrice" class="price-amount">$104,901.02</div>
            <div class="price-label">Bitcoin Price (Real-Time)</div>
        </div>

        <!-- Performance Grid -->
        <div class="performance-grid">
            <!-- Trading Performance -->
            <div class="performance-section">
                <div class="section-title">
                    📈 Trading Performance
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Equity</div>
                        <div id="equity" class="metric-value metric-neutral">$350.71</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total P&L</div>
                        <div id="totalPnL" class="metric-value metric-positive">$50.71</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Win Rate</div>
                        <div id="winRate" class="metric-value metric-neutral">100%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily P&L</div>
                        <div id="dailyPnL" class="metric-value metric-positive">$50.71</div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="performance-section">
                <div class="section-title">
                    🔧 System Status
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Open Positions</div>
                        <div id="openPositions" class="metric-value metric-neutral">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily Trades</div>
                        <div id="dailyTrades" class="metric-value metric-neutral">2</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total Trades</div>
                        <div id="totalTrades" class="metric-value metric-neutral">2</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Conservative Elite Score</div>
                        <div id="eliteScore" class="metric-value metric-neutral">79.1%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Trades Section -->
        <div class="trades-section">
            <div class="section-title">
                🔄 Recent Trades
            </div>
            <div id="recentTrades">
                <!-- Trades will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Status Cog -->
    <div class="status-cog" onclick="openStatusModal()">
        <div class="cog-icon">⚙️</div>
    </div>

    <!-- Auto-refresh Indicator -->
    <div class="refresh-indicator">
        <div class="refresh-spinner"></div>
        Auto-refresh: 5s
    </div>

    <!-- Status Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeStatusModal()">&times;</span>
            <h2>System Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Trading Engine</div>
                    <div id="tradingEngineStatus" class="status-value">Active</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Database</div>
                    <div id="databaseStatus" class="status-value">Connected</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Binance API</div>
                    <div id="binanceStatus" class="status-value">Connected</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Model</div>
                    <div id="modelStatus" class="status-value">Conservative Elite</div>
                </div>
                <div class="status-item">
                    <div class="status-label">AI Confidence</div>
                    <div id="aiConfidence" class="status-value">85%</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Last Update</div>
                    <div id="lastUpdate" class="status-value">Just now</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isRefreshing = false;

        // Modal functions
        function openStatusModal() {
            document.getElementById('statusModal').style.display = 'block';
            updateSystemStatus();
        }

        function closeStatusModal() {
            document.getElementById('statusModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('statusModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // Update functions
        async function updateDashboard() {
            if (isRefreshing) return;
            isRefreshing = true;

            try {
                // Update market data
                const marketResponse = await fetch('/api/market_data');
                const marketData = await marketResponse.json();
                
                document.getElementById('bitcoinPrice').textContent = 
                    `$${marketData.btc_price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;

                // Update portfolio data with cross margin information
                const portfolioResponse = await fetch('/api/portfolio_data');
                const portfolioData = await portfolioResponse.json();

                // Update equity with total available value (includes BTC for auto-rebalancing)
                document.getElementById('equity').textContent = `$${portfolioData.total_available_value.toFixed(2)}`;
                document.getElementById('totalPnL').textContent = `$${portfolioData.total_pnl.toFixed(2)}`;
                document.getElementById('dailyPnL').textContent = `$${portfolioData.daily_pnl.toFixed(2)}`;

                // Update equity label to show cross margin capability
                const equityLabel = document.querySelector('#equity').previousElementSibling;
                if (portfolioData.can_auto_rebalance) {
                    equityLabel.textContent = 'Total Available Equity';
                    equityLabel.title = `USDT: $${portfolioData.usdt_balance.toFixed(2)} + BTC: $${portfolioData.btc_value_usd.toFixed(2)} (auto-rebalanceable)`;
                } else {
                    equityLabel.textContent = 'Equity';
                    equityLabel.title = 'Current USDT balance only';
                }

                // Update system status
                const statusResponse = await fetch('/api/system_status');
                const statusData = await statusResponse.json();
                
                document.getElementById('openPositions').textContent = statusData.open_positions;
                document.getElementById('dailyTrades').textContent = statusData.daily_trades;
                document.getElementById('totalTrades').textContent = statusData.total_trades;
                document.getElementById('winRate').textContent = `${statusData.win_rate.toFixed(1)}%`;

                // Update live trading status with comprehensive checks
                const liveStatusResponse = await fetch('/api/live_trading_status');
                const liveStatus = await liveStatusResponse.json();

                const connectionStatus = document.getElementById('connectionStatus');
                if (liveStatus.system_ready && liveStatus.trading_active) {
                    connectionStatus.textContent = '🚀 LIVE TRADING ACTIVE';
                    connectionStatus.className = 'status-badge status-live';
                } else if (liveStatus.binance_connected && liveStatus.balance_sufficient) {
                    connectionStatus.textContent = '✅ READY FOR TRADING';
                    connectionStatus.className = 'status-badge status-live';
                } else if (liveStatus.binance_connected && !liveStatus.balance_sufficient) {
                    connectionStatus.textContent = '⚠️ INSUFFICIENT BALANCE';
                    connectionStatus.className = 'status-badge status-simulation';
                } else if (liveStatus.binance_connected) {
                    connectionStatus.textContent = '🔗 CONNECTED - STANDBY';
                    connectionStatus.className = 'status-badge status-simulation';
                } else {
                    connectionStatus.textContent = '❌ DISCONNECTED';
                    connectionStatus.className = 'status-badge status-error';
                }

                // Store live status for modal
                window.liveStatus = liveStatus;

                // Update recent trades
                await updateRecentTrades();

            } catch (error) {
                console.error('Error updating dashboard:', error);
                document.getElementById('connectionStatus').textContent = 'Connection Error';
                document.getElementById('connectionStatus').className = 'status-badge status-error';
            }

            isRefreshing = false;
        }

        async function updateRecentTrades() {
            try {
                const response = await fetch('/api/recent_trades');
                const trades = await response.json();
                
                const tradesContainer = document.getElementById('recentTrades');
                
                if (trades.length === 0) {
                    tradesContainer.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 20px;">No recent trades</div>';
                    return;
                }

                tradesContainer.innerHTML = trades.map(trade => {
                    const tradeAmount = (trade.quantity || 0) * (trade.entry_price || 0);
                    const profitLoss = trade.profit_loss || 0;
                    const profitPercent = tradeAmount > 0 ? (profitLoss / tradeAmount) * 100 : 0;
                    const isProfit = profitLoss > 0;
                    const statusIcon = isProfit ? '✅' : '❌';
                    const statusText = isProfit ? 'PROFIT' : 'LOSS';
                    const profitSign = isProfit ? '+' : '';

                    return `
                    <div class="trade-item">
                        <div class="trade-header">
                            <div>
                                <span class="trade-type">${(trade.direction || 'BUY').toUpperCase()}</span>
                                <span class="trade-status">${statusIcon} ${statusText}</span>
                                <span style="margin-left: 10px; opacity: 0.8;">Final Result</span>
                            </div>
                            <div class="trade-profit">${profitSign}$${Math.abs(profitLoss).toFixed(2)} (${profitSign}${Math.abs(profitPercent).toFixed(2)}%)</div>
                        </div>

                        <div class="trade-details">
                            <div class="trade-column">
                                <h4>📊 ENTRY</h4>
                                <div class="trade-info">
                                    Date: ${new Date(trade.timestamp || trade.created_at).toLocaleDateString()}<br>
                                    Time: ${new Date(trade.timestamp || trade.created_at).toLocaleTimeString()}<br>
                                    Price: $${(trade.entry_price || 0).toLocaleString()}<br>
                                    Amount: $${tradeAmount.toFixed(2)}<br>
                                    Size: ${(trade.quantity || 0).toFixed(6)} BTC
                                </div>
                            </div>
                            <div class="trade-column">
                                <h4>🔴 FINAL EXIT</h4>
                                <div class="trade-info">
                                    Date: ${new Date(trade.exit_time || trade.timestamp || trade.created_at).toLocaleDateString()}<br>
                                    Time: ${new Date(trade.exit_time || trade.timestamp || trade.created_at).toLocaleTimeString()}<br>
                                    Price: $${(trade.exit_price || trade.entry_price || 0).toLocaleString()}<br>
                                    Duration: ${trade.duration || 'Quick'}<br>
                                    Status: ${trade.status === 'CLOSED' ? '✅ Completed' : '🔄 Open'}
                                </div>
                            </div>
                        </div>

                        <div class="financial-summary">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <span style="color: #ffd700;">⚠️</span>
                                <strong>FINANCIAL SUMMARY</strong>
                            </div>
                            <div class="summary-grid">
                                <div class="summary-item">
                                    <div class="summary-label">Risk Amount</div>
                                    <div class="summary-value">$20.00</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">Target Profit</div>
                                    <div class="summary-value">$${(tradeAmount * 0.0025).toFixed(2)}</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">P&L Amount</div>
                                    <div class="summary-value">${profitSign}$${Math.abs(profitLoss).toFixed(2)}</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">Confidence</div>
                                    <div class="summary-value">${((trade.confidence || 0.85) * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                            <div style="margin-top: 10px; font-size: 0.8rem; opacity: 0.8;">
                                ID: ${trade.id || 'N/A'} | Grid Trading: 0.25% spacing
                                <span style="float: right;">Cross Margin</span>
                            </div>
                        </div>
                    </div>
                `;
                }).join('');
                
            } catch (error) {
                console.error('Error updating recent trades:', error);
            }
        }

        async function updateSystemStatus() {
            try {
                // Get system status
                const systemResponse = await fetch('/api/system_status');
                const systemStatus = await systemResponse.json();

                // Get AI status for dynamic confidence
                const aiResponse = await fetch('/api/ai_status');
                const aiStatus = await aiResponse.json();

                document.getElementById('tradingEngineStatus').textContent = systemStatus.trading_active ? 'Active' : 'Inactive';
                document.getElementById('databaseStatus').textContent = 'Connected';
                document.getElementById('binanceStatus').textContent = systemStatus.binance_connected ? 'Connected' : 'Disconnected';
                document.getElementById('modelStatus').textContent = 'Conservative Elite';

                // Use dynamic AI confidence from API
                document.getElementById('aiConfidence').textContent = aiStatus.confidence || '85%';
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

            } catch (error) {
                console.error('Error updating system status:', error);
                // Fallback values
                document.getElementById('aiConfidence').textContent = '85%';
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            setInterval(updateDashboard, 5000); // Update every 5 seconds
        });
    </script>
</body>
</html>
