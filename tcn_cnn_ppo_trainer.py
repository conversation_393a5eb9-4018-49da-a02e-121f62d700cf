#!/usr/bin/env python3
"""
TCN-CNN-PPO Training System for Conservative Elite
Trains until 90%+ win rate with comprehensive metrics
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import sqlite3
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

class TechnicalIndicators:
    """Calculate technical indicators for training"""
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all required indicators"""
        # VWAP (20-period)
        df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / df['close']
        
        # RSI (5-period)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (20-period)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ETH/BTC ratio (simplified)
        df['eth_btc_ratio'] = 0.065  # Placeholder
        
        # Price features
        df['price_change'] = df['close'].pct_change()
        df['volume_change'] = df['volume'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Grid features
        df['grid_spacing'] = 0.0025  # 0.25%
        df['grid_level'] = np.round(df['close'] / (df['close'] * df['grid_spacing'])) * (df['close'] * df['grid_spacing'])
        df['grid_distance'] = np.abs(df['close'] - df['grid_level']) / (df['close'] * df['grid_spacing'])
        
        return df

class TCNBlock(nn.Module):
    """Temporal Convolutional Network Block"""

    def __init__(self, in_channels, out_channels, kernel_size, dilation):
        super(TCNBlock, self).__init__()
        padding = (kernel_size - 1) * dilation // 2  # Causal padding
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, dilation=dilation, padding=padding)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, dilation=dilation, padding=padding)
        self.dropout = nn.Dropout(0.2)
        self.relu = nn.ReLU()
        self.residual = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        residual = self.residual(x)
        out = self.conv1(x)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.conv2(out)
        out = self.relu(out)
        out = self.dropout(out)

        # Ensure same size for residual connection
        if out.size(2) != residual.size(2):
            min_size = min(out.size(2), residual.size(2))
            out = out[:, :, :min_size]
            residual = residual[:, :, :min_size]

        return self.relu(out + residual)

class CNNFeatureExtractor(nn.Module):
    """CNN for spatial feature extraction"""
    
    def __init__(self, input_channels, hidden_dim):
        super(CNNFeatureExtractor, self).__init__()
        self.conv1 = nn.Conv1d(input_channels, hidden_dim, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = self.relu(self.conv1(x))
        x = self.dropout(x)
        x = self.relu(self.conv2(x))
        x = self.dropout(x)
        x = self.relu(self.conv3(x))
        return x

class TCN_CNN_PPO_Model(nn.Module):
    """Combined TCN-CNN-PPO Model"""
    
    def __init__(self, input_features=12, sequence_length=24, hidden_dim=128):
        super(TCN_CNN_PPO_Model, self).__init__()
        self.sequence_length = sequence_length
        self.input_features = input_features
        
        # CNN Feature Extractor
        self.cnn = CNNFeatureExtractor(input_features, hidden_dim)
        
        # TCN Layers
        self.tcn1 = TCNBlock(hidden_dim, hidden_dim, kernel_size=3, dilation=1)
        self.tcn2 = TCNBlock(hidden_dim, hidden_dim, kernel_size=3, dilation=2)
        self.tcn3 = TCNBlock(hidden_dim, hidden_dim, kernel_size=3, dilation=4)
        
        # PPO Actor-Critic heads
        self.actor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # BUY, SELL, HOLD
        )
        
        self.critic = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)  # Value estimation
        )
        
        self.confidence = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Confidence 0-1
        )
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, features)
        x = x.transpose(1, 2)  # (batch_size, features, sequence_length)
        
        # CNN feature extraction
        x = self.cnn(x)
        
        # TCN processing
        x = self.tcn1(x)
        x = self.tcn2(x)
        x = self.tcn3(x)
        
        # Global average pooling
        x = torch.mean(x, dim=2)  # (batch_size, hidden_dim)
        
        # Actor-Critic outputs
        action_logits = self.actor(x)
        value = self.critic(x)
        confidence = self.confidence(x)
        
        return action_logits, value, confidence

class PerformanceMetrics:
    """Calculate comprehensive performance metrics"""
    
    @staticmethod
    def calculate_sortino_ratio(returns: np.ndarray) -> float:
        """Calculate Sortino ratio"""
        if len(returns) == 0:
            return 0.0
        
        downside_returns = returns[returns < 0]
        if len(downside_returns) == 0:
            return 10.0  # Perfect case
        
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 10.0
        
        return np.mean(returns) / downside_deviation
    
    @staticmethod
    def calculate_ulcer_index(equity_curve: np.ndarray) -> float:
        """Calculate Ulcer Index"""
        if len(equity_curve) == 0:
            return 1.0
        
        running_max = np.maximum.accumulate(equity_curve)
        drawdowns = (equity_curve - running_max) / running_max * 100
        ulcer_index = np.sqrt(np.mean(drawdowns ** 2))
        return ulcer_index
    
    @staticmethod
    def calculate_equity_curve_r2(equity_curve: np.ndarray) -> float:
        """Calculate R² of equity curve vs linear trend"""
        if len(equity_curve) < 2:
            return 0.0
        
        x = np.arange(len(equity_curve))
        try:
            r2 = r2_score(equity_curve, np.polyval(np.polyfit(x, equity_curve, 1), x))
            return max(0, r2)
        except:
            return 0.0
    
    @staticmethod
    def calculate_profit_stability(returns: np.ndarray) -> float:
        """Calculate profit stability (inverse of return volatility)"""
        if len(returns) == 0:
            return 0.0
        
        volatility = np.std(returns)
        if volatility == 0:
            return 1.0
        
        return 1.0 / (1.0 + volatility)
    
    @staticmethod
    def calculate_upward_move_ratio(equity_curve: np.ndarray) -> float:
        """Calculate ratio of upward moves"""
        if len(equity_curve) < 2:
            return 0.5
        
        changes = np.diff(equity_curve)
        upward_moves = np.sum(changes > 0)
        total_moves = len(changes)
        
        return upward_moves / total_moves if total_moves > 0 else 0.5
    
    @staticmethod
    def calculate_drawdown_duration(equity_curve: np.ndarray) -> float:
        """Calculate average drawdown duration (inverse)"""
        if len(equity_curve) == 0:
            return 1.0
        
        running_max = np.maximum.accumulate(equity_curve)
        in_drawdown = equity_curve < running_max
        
        if not np.any(in_drawdown):
            return 1.0
        
        # Calculate drawdown durations
        durations = []
        current_duration = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                    current_duration = 0
        
        if current_duration > 0:
            durations.append(current_duration)
        
        avg_duration = np.mean(durations) if durations else 1
        return 1.0 / (1.0 + avg_duration)
    
    @staticmethod
    def calculate_composite_score(equity_curve: np.ndarray, returns: np.ndarray) -> float:
        """Calculate composite score with specified weights"""
        sortino = PerformanceMetrics.calculate_sortino_ratio(returns)
        sortino_norm = min(1.0, sortino / 3.0)  # Normalize to 0-1
        
        ulcer = PerformanceMetrics.calculate_ulcer_index(equity_curve)
        ulcer_index_inv = 1.0 / (1.0 + ulcer)
        
        equity_r2 = PerformanceMetrics.calculate_equity_curve_r2(equity_curve)
        profit_stability = PerformanceMetrics.calculate_profit_stability(returns)
        upward_ratio = PerformanceMetrics.calculate_upward_move_ratio(equity_curve)
        drawdown_duration_inv = PerformanceMetrics.calculate_drawdown_duration(equity_curve)
        
        composite_score = (
            0.25 * sortino_norm +
            0.20 * ulcer_index_inv +
            0.15 * equity_r2 +
            0.15 * profit_stability +
            0.15 * upward_ratio +
            0.10 * drawdown_duration_inv
        ) * 100  # Scale to 0-100
        
        return composite_score

class DataCollector:
    """Collect and prepare training data"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for training data")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def collect_training_data(self, days: int = 90) -> pd.DataFrame:
        """Collect 90 days of 1-hour data for training"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} days of training data...")
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data
            all_data = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                ohlcv = self.exchange.fetch_ohlcv(
                    'BTC/USDT', 
                    '1h',  # 1-hour timeframe
                    since=current_since, 
                    limit=1000
                )
                
                if not ohlcv:
                    break
                
                all_data.extend(ohlcv)
                current_since = ohlcv[-1][0] + 3600000  # Add 1 hour
                
                if len(all_data) % 1000 == 0:
                    print(f"📈 Collected {len(all_data)} candles...")
            
            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            # Calculate indicators
            df = TechnicalIndicators.calculate_all_indicators(df)
            
            print(f"✅ Collected {len(df)} candles with indicators")
            return df
            
        except Exception as e:
            print(f"❌ Error collecting training data: {e}")
            return pd.DataFrame()
    
    def prepare_sequences(self, df: pd.DataFrame, sequence_length: int = 24) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for training"""
        feature_columns = [
            'vwap_ratio', 'rsi_norm', 'bb_position', 'eth_btc_ratio',
            'price_change', 'volume_change', 'high_low_ratio', 'close_open_ratio',
            'grid_distance', 'open', 'high', 'low'
        ]
        
        # Remove NaN values
        df = df.dropna()
        
        if len(df) < sequence_length:
            print("❌ Insufficient data for sequences")
            return np.array([]), np.array([])
        
        # Normalize features
        scaler = StandardScaler()
        features = scaler.fit_transform(df[feature_columns].values)
        
        # Create sequences
        X, y = [], []
        
        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            
            # FIXED: Generate labels with corrected specifications
            current_price = df.iloc[i]['close']

            # FIXED: Relaxed grid proximity (0.1% instead of 0.01%)
            grid_size = current_price * 0.0025  # 0.25% grid spacing
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity = abs(current_price - grid_level) / grid_level

            if grid_proximity > 0.001:  # FIXED: 0.1% threshold (was 0.01%)
                label = 2  # HOLD
            else:
                # FIXED: Multi-hour lookhead for realistic movements
                best_price_change = 0
                for j in range(1, min(7, len(df) - i)):  # Look ahead up to 6 hours
                    future_price = df.iloc[i + j]['close']
                    price_change = (future_price - current_price) / current_price

                    # Take the best movement found
                    if abs(price_change) > abs(best_price_change):
                        best_price_change = price_change

                    # Stop if we found significant movement
                    if abs(price_change) >= 0.0015:  # FIXED: 0.15% threshold (was 0.25%)
                        break

                if best_price_change >= 0.0015:  # +0.15% profit -> BUY
                    label = 0
                elif best_price_change <= -0.0015:  # -0.15% profit -> SELL
                    label = 1
                else:  # No significant movement
                    label = 2  # HOLD
            
            y.append(label)
        
        return np.array(X), np.array(y)

def main():
    """Main training function"""
    print("🚀 STARTING TCN-CNN-PPO TRAINING")
    print("=" * 50)
    
    # Collect data
    collector = DataCollector()
    df = collector.collect_training_data(90)
    
    if df.empty:
        print("❌ Failed to collect training data")
        return
    
    # Split data: 60 days training, 30 days testing
    split_point = len(df) * 2 // 3
    train_df = df.iloc[:split_point]
    test_df = df.iloc[split_point:]
    
    print(f"📊 Training data: {len(train_df)} candles")
    print(f"📊 Testing data: {len(test_df)} candles")
    
    # Prepare sequences
    X_train, y_train = collector.prepare_sequences(train_df)
    X_test, y_test = collector.prepare_sequences(test_df)
    
    if len(X_train) == 0 or len(X_test) == 0:
        print("❌ Failed to prepare sequences")
        return
    
    print(f"✅ Training sequences: {X_train.shape}")
    print(f"✅ Testing sequences: {X_test.shape}")
    
    # Initialize model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = TCN_CNN_PPO_Model(input_features=12, sequence_length=24, hidden_dim=128).to(device)
    
    # Training setup
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.LongTensor(y_train).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.LongTensor(y_test).to(device)
    
    # Training loop
    best_win_rate = 0.0
    best_composite_score = 0.0
    best_net_profit = 0.0
    best_model_state = None
    
    print("\n🎯 Starting training loop...")
    print("Target: 90%+ win rate out of sample")
    
    for epoch in range(1000):  # Train until target reached
        model.train()
        
        # Training step
        optimizer.zero_grad()
        action_logits, values, confidence = model(X_train_tensor)
        loss = criterion(action_logits, y_train_tensor)
        loss.backward()
        optimizer.step()
        
        # Evaluation every 10 epochs
        if epoch % 10 == 0:
            model.eval()
            with torch.no_grad():
                test_logits, test_values, test_confidence = model(X_test_tensor)
                test_predictions = torch.argmax(test_logits, dim=1)
                
                # Calculate win rate
                correct = (test_predictions == y_test_tensor).float()
                win_rate = correct.mean().item() * 100
                
                # Simulate trading for metrics
                equity_curve, returns, net_profit = simulate_trading(
                    test_predictions.cpu().numpy(), 
                    test_df.iloc[24:]['close'].values,  # Skip sequence length
                    test_confidence.cpu().numpy()
                )
                
                composite_score = PerformanceMetrics.calculate_composite_score(equity_curve, returns)
                
                print(f"Epoch {epoch:4d} | Loss: {loss:.4f} | Win Rate: {win_rate:.1f}% | "
                      f"Composite: {composite_score:.1f} | Net Profit: ${net_profit:.2f}")
                
                # Save best models
                if win_rate > best_win_rate:
                    best_win_rate = win_rate
                    
                if composite_score > best_composite_score:
                    best_composite_score = composite_score
                    
                if net_profit > best_net_profit:
                    best_net_profit = net_profit
                    best_model_state = model.state_dict().copy()
                
                # Check if target reached
                if win_rate >= 90.0:
                    print(f"\n🎉 TARGET REACHED! Win Rate: {win_rate:.1f}%")
                    break
    
    # Save best model
    if best_model_state:
        torch.save(best_model_state, 'best_tcn_cnn_ppo_model.pth')
        print(f"\n💾 Best model saved:")
        print(f"   🏆 Best Win Rate: {best_win_rate:.1f}%")
        print(f"   📊 Best Composite Score: {best_composite_score:.1f}")
        print(f"   💰 Best Net Profit: ${best_net_profit:.2f}")
    
    return best_model_state, best_win_rate, best_composite_score, best_net_profit

def simulate_trading(predictions: np.ndarray, prices: np.ndarray, confidence: np.ndarray) -> Tuple[np.ndarray, np.ndarray, float]:
    """Simulate trading based on predictions with MULTI-HOUR LOOKHEAD"""
    balance = 300.0
    equity_curve = [balance]
    returns = []

    for i in range(len(predictions) - 6):  # Leave room for 6-hour lookhead
        if confidence[i] < 0.8:  # Skip low confidence signals
            equity_curve.append(balance)
            returns.append(0.0)
            continue

        current_price = prices[i]

        # FIXED: Multi-hour lookhead for realistic exits
        profit = 0.0

        if predictions[i] == 0:  # BUY
            # Look ahead up to 6 hours for exit
            for j in range(1, min(7, len(prices) - i)):
                future_price = prices[i + j]
                price_change = (future_price - current_price) / current_price

                if price_change >= 0.0015:  # +0.15% take profit
                    profit = 20.0  # $20 profit
                    break
                elif price_change <= -0.00075:  # -0.075% stop loss
                    profit = -10.0  # $10 loss
                    break

        elif predictions[i] == 1:  # SELL
            # Look ahead up to 6 hours for exit
            for j in range(1, min(7, len(prices) - i)):
                future_price = prices[i + j]
                price_change = (current_price - future_price) / current_price

                if price_change >= 0.0015:  # +0.15% take profit
                    profit = 20.0  # $20 profit
                    break
                elif price_change <= -0.00075:  # -0.075% stop loss
                    profit = -10.0  # $10 loss
                    break

        # Only update balance if trade exits
        if profit != 0.0:
            balance += profit
            returns.append(profit / balance if balance > 0 else 0)
        else:
            returns.append(0.0)

        equity_curve.append(balance)

    # Handle remaining predictions without lookhead
    for i in range(len(predictions) - 6, len(predictions)):
        equity_curve.append(balance)
        returns.append(0.0)

    net_profit = balance - 300.0
    return np.array(equity_curve), np.array(returns), net_profit

if __name__ == "__main__":
    main()
