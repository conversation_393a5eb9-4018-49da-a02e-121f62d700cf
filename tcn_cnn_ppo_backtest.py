#!/usr/bin/env python3
"""
TCN-CNN-PPO Backtest Runner
Runs comprehensive backtest with trained model
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
from sklearn.preprocessing import StandardScaler
from tcn_cnn_ppo_trainer import TCN_CNN_PPO_Model, TechnicalIndicators, PerformanceMetrics

class TCN_CNN_PPO_Backtest:
    """Comprehensive backtest using trained TCN-CNN-PPO model"""
    
    def __init__(self, model_path: str = "best_tcn_cnn_ppo_model.pth", api_key_file: str = "BinanceAPI_2.txt"):
        self.model_path = model_path
        self.api_key_file = api_key_file
        self.exchange = None
        self.model = None
        self.scaler = StandardScaler()
        
        # Trading parameters
        self.starting_balance = 300.0
        self.current_balance = 300.0
        self.risk_amount = 10.0
        self.profit_target = 20.0
        self.sequence_length = 24
        
        # Performance tracking
        self.trades = []
        self.equity_curve = [self.starting_balance]
        self.daily_returns = []
        
        self._connect_exchange()
        self._load_model()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for backtest")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def _load_model(self):
        """Load trained model"""
        try:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model = TCN_CNN_PPO_Model(input_features=12, sequence_length=24, hidden_dim=128).to(device)
            
            if torch.cuda.is_available():
                self.model.load_state_dict(torch.load(self.model_path))
            else:
                self.model.load_state_dict(torch.load(self.model_path, map_location='cpu'))
            
            self.model.eval()
            print("✅ Loaded trained TCN-CNN-PPO model")
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            self.model = None
    
    def collect_backtest_data(self, days: int = 30) -> pd.DataFrame:
        """Collect data for backtesting"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} days of backtest data...")
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days + 5)  # Extra days for indicators
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data
            all_data = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                ohlcv = self.exchange.fetch_ohlcv(
                    'BTC/USDT', 
                    '1h',  # 1-hour timeframe
                    since=current_since, 
                    limit=1000
                )
                
                if not ohlcv:
                    break
                
                all_data.extend(ohlcv)
                current_since = ohlcv[-1][0] + 3600000  # Add 1 hour
            
            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            # Calculate indicators
            df = TechnicalIndicators.calculate_all_indicators(df)
            
            # Take only the requested days (after indicators are calculated)
            df = df.tail(days * 24)  # 24 hours per day
            
            print(f"✅ Collected {len(df)} candles for backtest")
            return df
            
        except Exception as e:
            print(f"❌ Error collecting backtest data: {e}")
            return pd.DataFrame()
    
    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """Prepare features for model prediction"""
        feature_columns = [
            'vwap_ratio', 'rsi_norm', 'bb_position', 'eth_btc_ratio',
            'price_change', 'volume_change', 'high_low_ratio', 'close_open_ratio',
            'grid_distance', 'open', 'high', 'low'
        ]
        
        # Remove NaN values
        df_clean = df.dropna()
        
        if len(df_clean) < self.sequence_length:
            print("❌ Insufficient data for sequences")
            return np.array([])
        
        # Normalize features
        features = self.scaler.fit_transform(df_clean[feature_columns].values)
        
        # Create sequences
        sequences = []
        for i in range(self.sequence_length, len(features)):
            sequences.append(features[i-self.sequence_length:i])
        
        return np.array(sequences)
    
    def generate_predictions(self, sequences: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Generate predictions using trained model"""
        if self.model is None or len(sequences) == 0:
            return np.array([]), np.array([])
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        with torch.no_grad():
            sequences_tensor = torch.FloatTensor(sequences).to(device)
            action_logits, values, confidence = self.model(sequences_tensor)
            
            predictions = torch.argmax(action_logits, dim=1).cpu().numpy()
            confidence_scores = confidence.cpu().numpy().flatten()
        
        return predictions, confidence_scores
    
    def calculate_risk_amount(self, balance: float) -> float:
        """Calculate dynamic risk amount"""
        if balance <= 1000:
            return 10.0
        
        excess = balance - 1000
        additional_increments = int(excess // 500)
        return 10.0 + (additional_increments * 10.0)
    
    def execute_backtest(self, df: pd.DataFrame, predictions: np.ndarray, confidence: np.ndarray) -> Dict:
        """Execute comprehensive backtest"""
        print("🚀 Executing TCN-CNN-PPO backtest...")
        
        # Reset tracking variables
        self.current_balance = self.starting_balance
        self.trades = []
        self.equity_curve = [self.starting_balance]
        self.daily_returns = []
        
        # Skip initial rows used for sequences
        df_trading = df.iloc[self.sequence_length:].copy()
        
        if len(df_trading) != len(predictions):
            print(f"⚠️ Data length mismatch: {len(df_trading)} vs {len(predictions)}")
            min_len = min(len(df_trading), len(predictions))
            df_trading = df_trading.iloc[:min_len]
            predictions = predictions[:min_len]
            confidence = confidence[:min_len]
        
        open_trade = None
        last_trade_time = None
        trades_today = 0
        current_date = None
        
        for i in range(len(df_trading) - 1):
            current_time = df_trading.index[i]
            current_price = df_trading.iloc[i]['close']
            next_price = df_trading.iloc[i + 1]['close']
            
            # Reset daily trade count
            if current_date != current_time.date():
                current_date = current_time.date()
                trades_today = 0
            
            # Check for open trade exits first
            if open_trade:
                direction = open_trade['direction']
                entry_price = open_trade['entry_price']
                quantity = open_trade['quantity']
                
                profit_loss = 0
                exit_type = None
                
                if direction == "BUY":
                    # Check take profit (0.5%)
                    if current_price >= entry_price * 1.005:
                        profit_loss = self.profit_target
                        exit_type = "TP"
                    # Check stop loss (0.25%)
                    elif current_price <= entry_price * 0.9975:
                        profit_loss = -self.risk_amount
                        exit_type = "SL"
                
                elif direction == "SELL":
                    # Check take profit (0.5%)
                    if current_price <= entry_price * 0.995:
                        profit_loss = self.profit_target
                        exit_type = "TP"
                    # Check stop loss (0.25%)
                    elif current_price >= entry_price * 1.0025:
                        profit_loss = -self.risk_amount
                        exit_type = "SL"
                
                if exit_type:
                    # Close trade
                    self.current_balance += profit_loss
                    
                    trade_record = {
                        'entry_time': open_trade['entry_time'],
                        'exit_time': current_time,
                        'direction': direction,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'quantity': quantity,
                        'profit_loss': profit_loss,
                        'exit_type': exit_type,
                        'balance_after': self.current_balance
                    }
                    
                    self.trades.append(trade_record)
                    open_trade = None
                    
                    duration = (current_time - trade_record['entry_time']).total_seconds() / 3600
                    print(f"✅ {exit_type} {direction}: ${current_price:,.2f} | P&L: ${profit_loss:+.2f} | Duration: {duration:.1f}h")
            
            # Generate new signals if no open trade
            if not open_trade and confidence[i] > 0.8:
                # Check time and daily limits
                if last_trade_time and (current_time - last_trade_time).total_seconds() < 4 * 3600:
                    continue  # 4-hour minimum between trades
                
                if trades_today >= 6:
                    continue  # Maximum 6 trades per day
                
                prediction = predictions[i]
                
                if prediction in [0, 1]:  # BUY or SELL
                    direction = "BUY" if prediction == 0 else "SELL"
                    risk_amount = self.calculate_risk_amount(self.current_balance)
                    
                    # Calculate position size for $10 risk at 0.25% stop loss
                    stop_loss_pct = 0.0025
                    quantity = risk_amount / (current_price * stop_loss_pct)
                    
                    open_trade = {
                        'entry_time': current_time,
                        'direction': direction,
                        'entry_price': current_price,
                        'quantity': quantity,
                        'risk_amount': risk_amount,
                        'confidence': confidence[i]
                    }
                    
                    last_trade_time = current_time
                    trades_today += 1
                    
                    print(f"🎯 {direction}: ${current_price:,.2f} | Risk: ${risk_amount:.2f} | Confidence: {confidence[i]:.1%}")
            
            # Update equity curve
            self.equity_curve.append(self.current_balance)
            
            # Calculate daily return
            if len(self.equity_curve) > 1:
                daily_return = (self.equity_curve[-1] - self.equity_curve[-2]) / self.equity_curve[-2]
                self.daily_returns.append(daily_return)
        
        return self.calculate_results()
    
    def calculate_results(self) -> Dict:
        """Calculate comprehensive backtest results"""
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t['profit_loss'] > 0])
        losing_trades = total_trades - winning_trades
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_profit = sum(t['profit_loss'] for t in self.trades)
        total_return_pct = (self.current_balance - self.starting_balance) / self.starting_balance * 100
        
        # Calculate advanced metrics
        equity_array = np.array(self.equity_curve)
        returns_array = np.array(self.daily_returns)
        
        composite_score = PerformanceMetrics.calculate_composite_score(equity_array, returns_array)
        sortino_ratio = PerformanceMetrics.calculate_sortino_ratio(returns_array)
        ulcer_index = PerformanceMetrics.calculate_ulcer_index(equity_array)
        max_drawdown = np.max(np.maximum.accumulate(equity_array) - equity_array) / np.max(equity_array) * 100
        
        results = {
            'starting_balance': self.starting_balance,
            'ending_balance': self.current_balance,
            'total_profit': total_profit,
            'total_return_pct': total_return_pct,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'composite_score': composite_score,
            'sortino_ratio': sortino_ratio,
            'ulcer_index': ulcer_index,
            'max_drawdown': max_drawdown,
            'equity_curve': self.equity_curve,
            'trades': self.trades,
            'model_type': 'TCN-CNN-PPO'
        }
        
        return results
    
    def print_results(self, results: Dict):
        """Print comprehensive results"""
        print("\n" + "=" * 80)
        print("📊 TCN-CNN-PPO BACKTEST RESULTS")
        print("=" * 80)
        
        print(f"💰 Starting Balance: ${results['starting_balance']:,.2f}")
        print(f"💰 Ending Balance: ${results['ending_balance']:,.2f}")
        print(f"📈 Total Profit: ${results['total_profit']:+,.2f}")
        print(f"📊 Total Return: {results['total_return_pct']:+.2f}%")
        print(f"🎯 Total Trades: {results['total_trades']}")
        print(f"✅ Winning Trades: {results['winning_trades']}")
        print(f"❌ Losing Trades: {results['losing_trades']}")
        print(f"🏆 Win Rate: {results['win_rate']:.1f}%")
        print(f"📊 Composite Score: {results['composite_score']:.1f}")
        print(f"📈 Sortino Ratio: {results['sortino_ratio']:.2f}")
        print(f"📉 Ulcer Index: {results['ulcer_index']:.2f}")
        print(f"📉 Max Drawdown: {results['max_drawdown']:.2f}%")
        
        # Performance evaluation
        print("\n📊 PERFORMANCE EVALUATION:")
        if results['win_rate'] >= 90.0:
            print(f"✅ Win Rate: {results['win_rate']:.1f}% (EXCEEDS 90% threshold)")
        else:
            print(f"❌ Win Rate: {results['win_rate']:.1f}% (BELOW 90% threshold)")
        
        if results['composite_score'] > 79.0:
            print(f"✅ Composite Score: {results['composite_score']:.1f} (ABOVE 79 threshold)")
        else:
            print(f"❌ Composite Score: {results['composite_score']:.1f} (NOT ABOVE 79 threshold)")
        
        model_approved = results['win_rate'] >= 90.0 and results['composite_score'] > 79.0
        print(f"\n🤖 MODEL APPROVAL: {'✅ APPROVED' if model_approved else '❌ NOT APPROVED'}")
    
    def run_backtest(self, days: int = 30) -> Dict:
        """Run complete backtest"""
        print("🚀 STARTING TCN-CNN-PPO BACKTEST")
        print("=" * 50)
        
        # Collect data
        df = self.collect_backtest_data(days)
        if df.empty:
            return {}
        
        # Prepare features
        sequences = self.prepare_features(df)
        if len(sequences) == 0:
            return {}
        
        # Generate predictions
        predictions, confidence = self.generate_predictions(sequences)
        if len(predictions) == 0:
            return {}
        
        # Execute backtest
        results = self.execute_backtest(df, predictions, confidence)
        
        # Print results
        self.print_results(results)
        
        # Save results
        with open('tcn_cnn_ppo_backtest_results.json', 'w') as f:
            # Convert numpy arrays to lists for JSON serialization
            json_results = results.copy()
            json_results['equity_curve'] = [float(x) for x in results['equity_curve']]
            
            for trade in json_results['trades']:
                if 'entry_time' in trade:
                    trade['entry_time'] = trade['entry_time'].isoformat()
                if 'exit_time' in trade:
                    trade['exit_time'] = trade['exit_time'].isoformat()
            
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: tcn_cnn_ppo_backtest_results.json")
        
        return results

def main():
    """Main backtest function"""
    backtest = TCN_CNN_PPO_Backtest()
    results = backtest.run_backtest(30)
    
    if results:
        print("\n🎯 TCN-CNN-PPO backtest completed successfully!")
    else:
        print("❌ TCN-CNN-PPO backtest failed")

if __name__ == "__main__":
    main()
