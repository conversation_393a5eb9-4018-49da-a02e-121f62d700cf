#!/usr/bin/env python3
"""
Conservative Elite 30-Day Backtest
Tests the trading system performance over the last 30 days
"""

import ccxt
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json

class ConservativeEliteBacktest:
    """30-day backtest for Conservative Elite trading system"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self.starting_balance = 300.0
        self.current_balance = 300.0
        self.trades = []
        self.daily_balances = []
        
        # Trading parameters (CORRECTED: $10 RISK / $20 PROFIT - 2:1 RATIO)
        self.grid_spacing = 0.0025  # 0.25% (UNCHANGED)
        self.risk_amount_fixed = 10.0   # $10 risk per trade
        self.profit_target_fixed = 20.0 # $20 profit target (2:1 ratio)
        self.max_trades_per_day = 6
        self.min_hours_between_trades = 4
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = 300.0
        
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance for historical data"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for backtest data")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def calculate_risk_amount(self, account_balance: float) -> float:
        """Calculate risk amount based on corrected specifications"""
        if account_balance < 1000:
            return 10.0
        elif account_balance == 1000:
            return 20.0
        
        # For amounts above $1000
        excess_balance = account_balance - 1000
        additional_increments = int(excess_balance // 500)
        additional_risk = additional_increments * 10
        
        total_risk = 20.0 + additional_risk
        return total_risk
    
    def get_historical_data(self, days: int = 30) -> pd.DataFrame:
        """Get 30 days of 1-hour historical data"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return pd.DataFrame()
        
        print(f"📊 Fetching {days} days of historical data...")
        
        # Calculate start time
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data
            all_data = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                ohlcv = self.exchange.fetch_ohlcv(
                    'BTC/USDT', 
                    '1h',  # 1-hour timeframe for backtest
                    since=current_since, 
                    limit=1000
                )
                
                if not ohlcv:
                    break
                
                all_data.extend(ohlcv)
                current_since = ohlcv[-1][0] + 3600000  # Add 1 hour
                
            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            print(f"✅ Fetched {len(df)} candles from {df.index[0]} to {df.index[-1]}")
            return df
            
        except Exception as e:
            print(f"❌ Error fetching historical data: {e}")
            return pd.DataFrame()
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # VWAP (20-period)
        df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / df['close']
        
        # RSI (5-period)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (20-period)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        return df
    
    def generate_signal(self, row, last_trade_time: datetime) -> Tuple[Optional[str], float]:
        """Generate trading signal based on Conservative Elite logic"""
        current_time = row.name
        current_price = row['close']
        
        # Check time constraints
        if last_trade_time and (current_time - last_trade_time).total_seconds() < self.min_hours_between_trades * 3600:
            return None, 0.0
        
        # Check if indicators are available
        if pd.isna(row['vwap_ratio']) or pd.isna(row['rsi_norm']) or pd.isna(row['bb_position']):
            return None, 0.0
        
        # Grid proximity check
        grid_size = current_price * self.grid_spacing
        current_grid_level = round(current_price / grid_size) * grid_size
        distance_to_grid = abs(current_price - current_grid_level)
        grid_proximity = distance_to_grid / grid_size
        
        if grid_proximity > 0.1:  # Too far from grid
            return None, 0.0
        
        # Technical indicator signals
        vwap_ratio = row['vwap_ratio']
        rsi = row['rsi_norm']
        bb_position = row['bb_position']
        
        # Conservative Elite signal conditions
        buy_signal = (rsi < 0.4 and bb_position < 0.4 and vwap_ratio > 0.998)
        sell_signal = (rsi > 0.6 and bb_position > 0.6 and vwap_ratio < 1.002)
        
        if buy_signal and current_price <= current_grid_level:
            confidence = 0.85 + (0.1 * (1 - grid_proximity))
            return "BUY", confidence
        elif sell_signal and current_price >= current_grid_level:
            confidence = 0.85 + (0.1 * (1 - grid_proximity))
            return "SELL", confidence
        
        return None, 0.0
    
    def execute_trade(self, direction: str, entry_price: float, entry_time: datetime, confidence: float):
        """Execute a backtest trade with REALISTIC PERCENTAGE-BASED EXITS"""
        # Use percentage-based approach for realistic exits
        # Stop loss: 0.25% (grid spacing), Take profit: 0.5% (2:1 ratio)
        stop_loss_pct = 0.0025  # 0.25%
        take_profit_pct = 0.005  # 0.5% (2x the stop loss for 2:1 ratio)

        # Calculate position size to risk exactly $10 at stop loss
        risk_amount = self.risk_amount_fixed  # $10
        quantity = risk_amount / (entry_price * stop_loss_pct)

        # Calculate exit levels based on PERCENTAGES
        if direction == "BUY":
            take_profit_price = entry_price * (1 + take_profit_pct)  # +0.5%
            stop_loss_price = entry_price * (1 - stop_loss_pct)      # -0.25%
        else:  # SELL
            take_profit_price = entry_price * (1 - take_profit_pct)  # -0.5%
            stop_loss_price = entry_price * (1 + stop_loss_pct)      # +0.25%
        
        trade = {
            'id': len(self.trades) + 1,
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': entry_time,
            'quantity': quantity,
            'risk_amount': risk_amount,
            'take_profit_price': take_profit_price,
            'stop_loss_price': stop_loss_price,
            'confidence': confidence,
            'status': 'OPEN',
            'exit_price': None,
            'exit_time': None,
            'profit_loss': 0.0
        }
        
        self.trades.append(trade)
        self.total_trades += 1
        
        print(f"🎯 {direction} Trade #{trade['id']}: ${entry_price:,.2f} | Risk: $10 | Target: $20 | Confidence: {confidence:.1%}")
        
        return trade
    
    def check_trade_exits(self, df: pd.DataFrame):
        """Check for trade exits in historical data"""
        open_trades = [t for t in self.trades if t['status'] == 'OPEN']
        
        for trade in open_trades:
            entry_time = trade['entry_time']
            
            # Get data after entry time
            future_data = df[df.index > entry_time]
            
            for timestamp, row in future_data.iterrows():
                current_price = row['close']
                
                # Check exit conditions with PERCENTAGE-BASED EXITS
                if trade['direction'] == "BUY":
                    if current_price >= trade['take_profit_price']:
                        # Take profit hit - Calculate actual P&L
                        profit_loss = (trade['take_profit_price'] - trade['entry_price']) * trade['quantity']
                        self._close_trade(trade, trade['take_profit_price'], timestamp, profit_loss, "TP")
                        break
                    elif current_price <= trade['stop_loss_price']:
                        # Stop loss hit - Calculate actual P&L
                        profit_loss = (trade['stop_loss_price'] - trade['entry_price']) * trade['quantity']
                        self._close_trade(trade, trade['stop_loss_price'], timestamp, profit_loss, "SL")
                        break

                else:  # SELL
                    if current_price <= trade['take_profit_price']:
                        # Take profit hit - Calculate actual P&L
                        profit_loss = (trade['entry_price'] - trade['take_profit_price']) * trade['quantity']
                        self._close_trade(trade, trade['take_profit_price'], timestamp, profit_loss, "TP")
                        break
                    elif current_price >= trade['stop_loss_price']:
                        # Stop loss hit - Calculate actual P&L
                        profit_loss = (trade['entry_price'] - trade['stop_loss_price']) * trade['quantity']
                        self._close_trade(trade, trade['stop_loss_price'], timestamp, profit_loss, "SL")
                        break
    
    def _close_trade(self, trade: dict, exit_price: float, exit_time: datetime, profit_loss: float, exit_type: str):
        """Close a trade and update statistics"""
        trade['status'] = 'CLOSED'
        trade['exit_price'] = exit_price
        trade['exit_time'] = exit_time
        trade['profit_loss'] = profit_loss
        trade['exit_type'] = exit_type
        
        # Update balance
        self.current_balance += profit_loss
        self.total_profit += profit_loss
        
        # Update statistics
        if profit_loss > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Track drawdown
        if self.current_balance > self.peak_balance:
            self.peak_balance = self.current_balance
        
        current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        duration = (exit_time - trade['entry_time']).total_seconds() / 3600  # hours
        
        print(f"✅ {exit_type} {trade['direction']} Trade #{trade['id']}: ${exit_price:,.2f} | P&L: ${profit_loss:+.2f} | Duration: {duration:.1f}h")
    
    def run_backtest(self) -> Dict:
        """Run the complete 30-day backtest with $10 RISK / $20 PROFIT (2:1 RATIO)"""
        print("🚀 STARTING CONSERVATIVE ELITE 30-DAY BACKTEST ($10 RISK / $20 PROFIT - 2:1 RATIO)")
        print("=" * 80)
        
        # Get historical data
        df = self.get_historical_data(30)
        if df.empty:
            return {}
        
        # Calculate indicators
        df = self.calculate_indicators(df)
        
        # Track daily trades
        daily_trade_count = {}
        last_trade_time = None
        
        # Process each candle
        for timestamp, row in df.iterrows():
            current_date = timestamp.date()
            
            # Reset daily trade count
            if current_date not in daily_trade_count:
                daily_trade_count[current_date] = 0
            
            # Check daily trade limit
            if daily_trade_count[current_date] >= self.max_trades_per_day:
                continue
            
            # Generate signal
            direction, confidence = self.generate_signal(row, last_trade_time)
            
            if direction and confidence > 0.8:
                # Execute trade
                trade = self.execute_trade(direction, row['close'], timestamp, confidence)
                daily_trade_count[current_date] += 1
                last_trade_time = timestamp
        
        # Check all trade exits
        self.check_trade_exits(df)
        
        # Calculate final results
        return self.calculate_results()
    
    def calculate_results(self) -> Dict:
        """Calculate backtest results"""
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        # Calculate composite score (simplified)
        profit_factor = abs(self.total_profit / min(-1, sum(t['profit_loss'] for t in self.trades if t['profit_loss'] < 0))) if any(t['profit_loss'] < 0 for t in self.trades) else 1
        composite_score = (win_rate * 0.4) + (profit_factor * 20) + ((1 - self.max_drawdown) * 40)
        
        results = {
            'starting_balance': self.starting_balance,
            'ending_balance': self.current_balance,
            'total_profit': self.total_profit,
            'total_return_pct': (self.current_balance - self.starting_balance) / self.starting_balance * 100,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown * 100,
            'profit_factor': profit_factor,
            'composite_score': composite_score,
            'trades': self.trades
        }
        
        return results
    
    def print_results(self, results: Dict):
        """Print backtest results"""
        print("\n" + "=" * 80)
        print("📊 CONSERVATIVE ELITE 30-DAY BACKTEST RESULTS ($10 RISK / $20 PROFIT - 2:1 RATIO)")
        print("=" * 80)
        
        print(f"💰 Starting Balance: ${results['starting_balance']:,.2f}")
        print(f"💰 Ending Balance: ${results['ending_balance']:,.2f}")
        print(f"📈 Total Profit: ${results['total_profit']:+,.2f}")
        print(f"📊 Total Return: {results['total_return_pct']:+.2f}%")
        print(f"🎯 Total Trades: {results['total_trades']}")
        print(f"✅ Winning Trades: {results['winning_trades']}")
        print(f"❌ Losing Trades: {results['losing_trades']}")
        print(f"🏆 Win Rate: {results['win_rate']:.1f}%")
        print(f"📉 Max Drawdown: {results['max_drawdown']:.2f}%")
        print(f"⚡ Profit Factor: {results['profit_factor']:.2f}")
        print(f"🎯 Composite Score: {results['composite_score']:.1f}")
        
        # Performance evaluation
        print("\n📊 PERFORMANCE EVALUATION:")
        if results['win_rate'] >= 93.0:
            print(f"✅ Win Rate: {results['win_rate']:.1f}% (EXCEEDS 93% threshold)")
        else:
            print(f"❌ Win Rate: {results['win_rate']:.1f}% (BELOW 93% threshold)")
        
        if results['composite_score'] > 79.0:
            print(f"✅ Composite Score: {results['composite_score']:.1f} (ABOVE 79 threshold)")
        else:
            print(f"❌ Composite Score: {results['composite_score']:.1f} (NOT ABOVE 79 threshold)")
        
        model_approved = results['win_rate'] >= 93.0 and results['composite_score'] > 79.0
        print(f"\n🤖 MODEL APPROVAL: {'✅ APPROVED' if model_approved else '❌ NOT APPROVED'}")

def main():
    """Run the 30-day backtest"""
    backtest = ConservativeEliteBacktest()
    results = backtest.run_backtest()
    
    if results:
        backtest.print_results(results)
        
        # Save results to file
        with open('backtest_results_30day_2to1.json', 'w') as f:
            # Convert datetime objects to strings for JSON serialization
            json_results = results.copy()
            for trade in json_results['trades']:
                if 'entry_time' in trade and trade['entry_time']:
                    trade['entry_time'] = trade['entry_time'].isoformat()
                if 'exit_time' in trade and trade['exit_time']:
                    trade['exit_time'] = trade['exit_time'].isoformat()
            
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: backtest_results_30day_2to1.json")
    else:
        print("❌ Backtest failed")

if __name__ == "__main__":
    main()
