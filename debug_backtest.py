#!/usr/bin/env python3
"""
Debug version to investigate why no trades are being generated
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json

class DebugBacktest:
    """Debug version to find the issue"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self.grid_spacing = 0.0025  # 0.25%
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for debug")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def get_sample_data(self, days: int = 1) -> pd.DataFrame:
        """Get small sample of data for debugging"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} day of data for debugging...")
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data
            ohlcv = self.exchange.fetch_ohlcv(
                'BTC/USDT', 
                '1h',  # 1-hour timeframe
                since=since, 
                limit=24  # Just 24 hours
            )
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            print(f"✅ Collected {len(df)} candles")
            print(f"📈 Price range: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error collecting data: {e}")
            return pd.DataFrame()
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate basic indicators"""
        print("📊 Calculating indicators...")
        
        # VWAP (simplified - use close price if volume issues)
        try:
            df['vwap'] = (df['close'] * df['volume']).rolling(5).sum() / df['volume'].rolling(5).sum()
            df['vwap_ratio'] = df['vwap'] / df['close']
        except:
            df['vwap_ratio'] = 1.0  # Fallback
        
        # RSI (simplified)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(3).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(3).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (simplified)
        df['bb_middle'] = df['close'].rolling(5).mean()
        df['bb_std'] = df['close'].rolling(5).std()
        df['bb_upper'] = df['bb_middle'] + (1 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (1 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0.5)
        
        print(f"✅ Indicators calculated")
        print(f"📊 VWAP ratio range: {df['vwap_ratio'].min():.4f} - {df['vwap_ratio'].max():.4f}")
        print(f"📊 RSI range: {df['rsi_norm'].min():.4f} - {df['rsi_norm'].max():.4f}")
        print(f"📊 BB position range: {df['bb_position'].min():.4f} - {df['bb_position'].max():.4f}")
        
        return df
    
    def check_grid_proximity(self, price: float) -> Tuple[float, float, float]:
        """Check grid proximity and return details"""
        grid_size = price * self.grid_spacing  # 0.25%
        grid_level = round(price / grid_size) * grid_size
        grid_proximity_ratio = abs(price - grid_level) / grid_level
        
        return grid_level, grid_proximity_ratio, grid_size
    
    def generate_signal_debug(self, row) -> Tuple[Optional[str], float, Dict]:
        """Generate signal with detailed debugging"""
        current_price = row['close']
        
        debug_info = {
            'price': current_price,
            'indicators_available': True,
            'grid_check': {},
            'signal_conditions': {},
            'final_decision': None
        }
        
        # Check if indicators are available
        if pd.isna(row['vwap_ratio']) or pd.isna(row['rsi_norm']) or pd.isna(row['bb_position']):
            debug_info['indicators_available'] = False
            return None, 0.0, debug_info
        
        # Grid proximity check
        grid_level, grid_proximity_ratio, grid_size = self.check_grid_proximity(current_price)
        
        debug_info['grid_check'] = {
            'grid_level': grid_level,
            'grid_proximity_ratio': grid_proximity_ratio,
            'grid_size': grid_size,
            'proximity_threshold': 0.0001,
            'passes_proximity': grid_proximity_ratio <= 0.0001
        }
        
        # Technical indicator values
        vwap_ratio = row['vwap_ratio']
        rsi = row['rsi_norm']
        bb_position = row['bb_position']
        
        debug_info['signal_conditions'] = {
            'vwap_ratio': vwap_ratio,
            'rsi_norm': rsi,
            'bb_position': bb_position,
            'buy_conditions': {
                'rsi_low': rsi < 0.4,
                'bb_low': bb_position < 0.4,
                'vwap_high': vwap_ratio > 0.998
            },
            'sell_conditions': {
                'rsi_high': rsi > 0.6,
                'bb_high': bb_position > 0.6,
                'vwap_low': vwap_ratio < 1.002
            }
        }
        
        # Check if grid proximity passes
        if grid_proximity_ratio > 0.0001:  # 0.01% threshold
            debug_info['final_decision'] = f"REJECTED: Too far from grid ({grid_proximity_ratio:.6f} > 0.0001)"
            return None, 0.0, debug_info
        
        # Signal conditions
        buy_signal = (rsi < 0.4 and bb_position < 0.4 and vwap_ratio > 0.998)
        sell_signal = (rsi > 0.6 and bb_position > 0.6 and vwap_ratio < 1.002)
        
        debug_info['signal_conditions']['buy_signal'] = buy_signal
        debug_info['signal_conditions']['sell_signal'] = sell_signal
        
        if buy_signal:
            confidence = 0.95 - (grid_proximity_ratio * 1000)
            confidence = max(0.80, min(0.99, confidence))
            debug_info['final_decision'] = f"BUY signal with confidence {confidence:.3f}"
            return "BUY", confidence, debug_info
        elif sell_signal:
            confidence = 0.95 - (grid_proximity_ratio * 1000)
            confidence = max(0.80, min(0.99, confidence))
            debug_info['final_decision'] = f"SELL signal with confidence {confidence:.3f}"
            return "SELL", confidence, debug_info
        else:
            debug_info['final_decision'] = "No signal conditions met"
            return None, 0.0, debug_info
    
    def run_debug(self):
        """Run debug analysis"""
        print("🔍 STARTING DEBUG ANALYSIS")
        print("=" * 50)
        
        # Get sample data
        df = self.get_sample_data(1)
        if df.empty:
            return
        
        # Calculate indicators
        df = self.calculate_indicators(df)
        
        print(f"\n📊 ANALYZING {len(df)} CANDLES:")
        print("=" * 50)
        
        signal_count = 0
        grid_rejections = 0
        indicator_rejections = 0
        
        for i, (timestamp, row) in enumerate(df.iterrows()):
            print(f"\n🕐 Candle {i+1}: {timestamp.strftime('%Y-%m-%d %H:%M')} | Price: ${row['close']:,.2f}")
            
            direction, confidence, debug_info = self.generate_signal_debug(row)
            
            if not debug_info['indicators_available']:
                indicator_rejections += 1
                print("   ❌ Indicators not available")
                continue
            
            # Print grid analysis
            grid_info = debug_info['grid_check']
            print(f"   📏 Grid Level: ${grid_info['grid_level']:,.2f}")
            print(f"   📏 Grid Proximity: {grid_info['grid_proximity_ratio']:.8f} ({'✅ PASS' if grid_info['passes_proximity'] else '❌ FAIL'})")
            
            if not grid_info['passes_proximity']:
                grid_rejections += 1
                print(f"   🚫 {debug_info['final_decision']}")
                continue
            
            # Print indicator analysis
            conditions = debug_info['signal_conditions']
            print(f"   📊 VWAP Ratio: {conditions['vwap_ratio']:.6f}")
            print(f"   📊 RSI: {conditions['rsi_norm']:.3f}")
            print(f"   📊 BB Position: {conditions['bb_position']:.3f}")
            
            # Print buy conditions
            buy_cond = conditions['buy_conditions']
            print(f"   🟢 BUY: RSI<0.4({buy_cond['rsi_low']}) & BB<0.4({buy_cond['bb_low']}) & VWAP>0.998({buy_cond['vwap_high']}) = {conditions.get('buy_signal', False)}")
            
            # Print sell conditions  
            sell_cond = conditions['sell_conditions']
            print(f"   🔴 SELL: RSI>0.6({sell_cond['rsi_high']}) & BB>0.6({sell_cond['bb_high']}) & VWAP<1.002({sell_cond['vwap_low']}) = {conditions.get('sell_signal', False)}")
            
            if direction:
                signal_count += 1
                print(f"   ✅ {debug_info['final_decision']}")
            else:
                print(f"   ⚪ {debug_info['final_decision']}")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total Candles: {len(df)}")
        print(f"   Signals Generated: {signal_count}")
        print(f"   Grid Rejections: {grid_rejections}")
        print(f"   Indicator Rejections: {indicator_rejections}")
        print(f"   No Signal Conditions: {len(df) - signal_count - grid_rejections - indicator_rejections}")
        
        if signal_count == 0:
            print(f"\n🚨 ROOT CAUSE ANALYSIS:")
            if grid_rejections > 0:
                print(f"   - {grid_rejections} candles rejected due to grid proximity (need to be within 0.01% of grid level)")
                print(f"   - Consider relaxing grid proximity threshold from 0.0001 to 0.001 (0.1%)")
            
            if indicator_rejections > 0:
                print(f"   - {indicator_rejections} candles had missing indicators")
            
            remaining = len(df) - signal_count - grid_rejections - indicator_rejections
            if remaining > 0:
                print(f"   - {remaining} candles didn't meet technical indicator conditions")
                print(f"   - Consider relaxing RSI/BB/VWAP thresholds")

def main():
    """Run debug analysis"""
    debug = DebugBacktest()
    debug.run_debug()

if __name__ == "__main__":
    main()
