#!/usr/bin/env python3
"""
TCN-CNN-PPO DEPLOYMENT SCRIPT
Advanced ML trading system deployment with comprehensive validation
"""

import subprocess
import sys
import os
import time
import signal
from datetime import datetime
import json

class TCN_CNN_PPO_Deployment:
    """Manages deployment of the advanced TCN-CNN-PPO trading system"""
    
    def __init__(self):
        self.processes = []
        self.is_running = False
    
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 CHECKING TCN-CNN-PPO DEPENDENCIES...")
        
        required_packages = {
            'ccxt': 'Trading exchange connectivity',
            'pandas': 'Data processing',
            'numpy': 'Numerical computations',
            'torch': 'PyTorch deep learning framework',
            'stable_baselines3': 'Reinforcement learning',
            'gym': 'RL environment',
            'sklearn': 'Machine learning utilities',
            'flask': 'Web application framework'
        }
        
        missing_packages = []
        
        for package, description in required_packages.items():
            try:
                if package == 'sklearn':
                    __import__('sklearn')
                else:
                    __import__(package)
                print(f"  ✅ {package} - {description}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package} - MISSING ({description})")
        
        if missing_packages:
            print(f"\n🚨 Missing packages: {', '.join(missing_packages)}")
            print("📦 Install with: pip install -r requirements_tcn_cnn_ppo.txt")
            return False
        
        return True
    
    def check_api_keys(self):
        """Check if API key file exists"""
        if os.path.exists('BinanceAPI_2.txt'):
            print("✅ API key file found")
            return True
        else:
            print("❌ BinanceAPI_2.txt not found")
            print("📝 Please create BinanceAPI_2.txt with your Binance API credentials:")
            print("   Line 1: API Key")
            print("   Line 2: Secret Key")
            return False
    
    def check_system_files(self):
        """Check if required system files exist"""
        print("📁 CHECKING SYSTEM FILES...")
        
        required_files = {
            'tcn_cnn_ppo_system.py': 'Main TCN-CNN-PPO trading system',
            'trading_webapp.py': 'Web application (if using web interface)',
            'BinanceAPI_2.txt': 'API credentials'
        }
        
        missing_files = []
        
        for file, description in required_files.items():
            if os.path.exists(file):
                print(f"  ✅ {file} - {description}")
            else:
                if file != 'trading_webapp.py':  # Optional file
                    missing_files.append(file)
                print(f"  ❌ {file} - MISSING ({description})")
        
        if missing_files:
            print(f"\n🚨 Missing required files: {', '.join(missing_files)}")
            return False
        
        return True
    
    def validate_ml_models(self):
        """Validate ML model initialization"""
        print("🧠 VALIDATING ML MODELS...")
        
        try:
            # Test model imports
            from tcn_cnn_ppo_system import TCN_CNN_Model, TradingEnvironment
            print("  ✅ TCN-CNN-PPO models import successfully")
            
            # Test model initialization
            model = TCN_CNN_Model(num_features=25, sequence_length=60, num_actions=3)
            print("  ✅ TCN-CNN model initializes correctly")
            
            # Test environment
            import pandas as pd
            dummy_data = pd.DataFrame({
                'open': [100] * 100,
                'high': [101] * 100,
                'low': [99] * 100,
                'close': [100] * 100,
                'volume': [1000] * 100
            })
            env = TradingEnvironment(dummy_data)
            print("  ✅ Trading environment initializes correctly")
            
            return True
            
        except Exception as e:
            print(f"  ❌ ML model validation failed: {e}")
            return False
    
    def start_tcn_cnn_ppo_system(self):
        """Start the TCN-CNN-PPO trading system"""
        print("🚀 Starting TCN-CNN-PPO Trading System...")
        
        try:
            # Start trading system in background
            process = subprocess.Popen([
                sys.executable, 'tcn_cnn_ppo_system.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(('tcn_cnn_ppo_system', process))
            print("✅ TCN-CNN-PPO Trading System started")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start TCN-CNN-PPO system: {e}")
            return False
    
    def start_web_app(self):
        """Start the web application (optional)"""
        if not os.path.exists('trading_webapp.py'):
            print("⚠️  Web application not found, skipping...")
            return True
        
        print("🌐 Starting Web Application...")
        
        try:
            # Start web app in background
            process = subprocess.Popen([
                sys.executable, 'trading_webapp.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(('web_app', process))
            print("✅ Web Application started")
            print("📊 Dashboard: http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start web app: {e}")
            return False
    
    def check_processes(self):
        """Check if processes are still running"""
        running_processes = []
        
        for name, process in self.processes:
            if process.poll() is None:  # Still running
                running_processes.append((name, process))
            else:
                print(f"⚠️ {name} process stopped")
        
        self.processes = running_processes
        return len(self.processes) > 0
    
    def stop_all(self):
        """Stop all processes"""
        print("\n🛑 Stopping all processes...")
        
        for name, process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=10)
                print(f"✅ {name} stopped")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 {name} force killed")
            except Exception as e:
                print(f"❌ Error stopping {name}: {e}")
        
        self.processes = []
        self.is_running = False
    
    def deploy(self):
        """Deploy the complete TCN-CNN-PPO trading system"""
        print("🚀 TCN-CNN-PPO ADVANCED TRADING SYSTEM DEPLOYMENT")
        print("=" * 70)
        print(f"📅 Deployment Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Pre-deployment checks
        print("🔍 PRE-DEPLOYMENT VALIDATION:")
        print("-" * 40)
        
        if not self.check_dependencies():
            print("\n❌ Dependency check failed. Please install missing packages.")
            return False
        
        if not self.check_api_keys():
            print("\n❌ API key check failed. Please configure API keys.")
            return False
        
        if not self.check_system_files():
            print("\n❌ System files check failed. Please ensure all files are present.")
            return False
        
        if not self.validate_ml_models():
            print("\n❌ ML model validation failed. Please check model implementations.")
            return False
        
        print("\n✅ All pre-deployment checks passed!")
        print()
        
        # Start services
        print("🚀 STARTING ADVANCED ML SERVICES:")
        print("-" * 35)
        
        if not self.start_tcn_cnn_ppo_system():
            print("\n❌ Failed to start TCN-CNN-PPO system")
            return False
        
        # Wait a moment for system to initialize
        time.sleep(5)
        
        # Start web app (optional)
        self.start_web_app()
        
        # Wait for web app to start
        time.sleep(3)
        
        self.is_running = True
        
        print("\n🎉 DEPLOYMENT SUCCESSFUL!")
        print("=" * 50)
        print("🧠 TCN-CNN-PPO System: ACTIVE")
        print("📊 Web Dashboard: http://localhost:5000 (if available)")
        print("📝 Trading Logs: tcn_cnn_ppo_trading.log")
        print("💾 State File: tcn_cnn_ppo_state.json")
        print()
        print("🤖 ADVANCED ML CONFIGURATION:")
        print("   - Neural Networks: TCN + CNN + PPO")
        print("   - Indicators: 25 comprehensive indicators")
        print("   - Robust Metrics: Complete scoring system")
        print("   - Training Target: 90%+ win rate")
        print("   - Robust Score Target: 0.79+")
        print("   - Auto-Integration: When targets achieved")
        print()
        print("📈 EXPECTED PERFORMANCE:")
        print("   - Win Rate: 90%+ (ML target)")
        print("   - Robust Score: 0.79+ (normalized)")
        print("   - Risk Management: AI-enhanced")
        print("   - Signal Quality: Neural network confidence")
        print()
        print("⚠️  IMPORTANT NOTES:")
        print("   - System starts in SIMULATION mode")
        print("   - Training pipeline ready for execution")
        print("   - Real-time ML inference active")
        print("   - Independent operation from development")
        print("   - Auto-deploys when ML targets achieved")
        print()
        print("🛑 To stop: Press Ctrl+C or use web dashboard")
        
        return True
    
    def monitor(self):
        """Monitor the running system"""
        try:
            while self.is_running:
                if not self.check_processes():
                    print("\n⚠️ All processes stopped")
                    break
                
                time.sleep(30)  # Check every 30 seconds
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown signal received")
        
        finally:
            self.stop_all()

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutdown signal received")
    sys.exit(0)

def main():
    """Main deployment function"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create deployment manager
    deployment = TCN_CNN_PPO_Deployment()
    
    try:
        # Deploy the system
        if deployment.deploy():
            # Monitor the system
            deployment.monitor()
        else:
            print("\n❌ Deployment failed")
            sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ Deployment error: {e}")
        deployment.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main()
