
        <!DOCTYPE html>
        <html>
        <head>
            <title>🔒 Enhanced Training System Report - 60/30 Day Split</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .header { background: #2c3e50; color: white; padding: 20px; border-radius: 10px; }
                .section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; }
                .metric { display: inline-block; margin: 10px; padding: 15px; background: #ecf0f1; border-radius: 5px; }
                .success { color: #27ae60; font-weight: bold; }
                .warning { color: #e74c3c; font-weight: bold; }
                .locked { color: #8e44ad; font-weight: bold; }
                .best { background: #f39c12; color: white; padding: 5px; border-radius: 3px; }
                .profit { background: #27ae60; color: white; padding: 5px; border-radius: 3px; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background: #34495e; color: white; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔒 ENHANCED TRAINING SYSTEM REPORT</h1>
                <p>Generated: 2025-06-07 18:50:45 UTC</p>
                <p class="locked">🔒 60 DAY TRAINING / 30 DAY TESTING - COMPOSITE SCORE & NET PROFIT OPTIMIZATION</p>
            </div>

            <div class="section">
                <h2>🏆 BEST COMPOSITE SCORE MODEL</h2>
                <div class="metric best">🏆 <strong>Model ID:</strong> enhanced_tcn_cnn_ppo_20250607_184858_5</div>
                <div class="metric">📈 <strong>Total Trades:</strong> 21337</div>
                <div class="metric">🎯 <strong>Win Rate:</strong> 37.31%</div>
                <div class="metric">🔒 <strong>Composite Score:</strong> 0.5087</div>
                <div class="metric">💰 <strong>Net Profit:</strong> $-0.83</div>
                <div class="metric">📊 <strong>Return:</strong> -0.28%</div>
                <div class="metric">📉 <strong>Max Drawdown:</strong> 1.20%</div>
            </div>

            <div class="section">
                <h2>💰 BEST NET PROFIT MODEL</h2>
                <div class="metric profit">💰 <strong>Model ID:</strong> enhanced_tcn_cnn_ppo_20250607_184858_5</div>
                <div class="metric">📈 <strong>Total Trades:</strong> 21337</div>
                <div class="metric">🎯 <strong>Win Rate:</strong> 37.31%</div>
                <div class="metric">🔒 <strong>Composite Score:</strong> 0.5087</div>
                <div class="metric">💰 <strong>Net Profit:</strong> $-0.83</div>
                <div class="metric">📊 <strong>Return:</strong> -0.28%</div>
                <div class="metric">📉 <strong>Max Drawdown:</strong> 1.20%</div>
            </div>

            <div class="section">
                <h2>🔒 LOCKED COMPOSITE SCORE BREAKDOWN (BEST MODEL)</h2>
                <table>
                    <tr><th>Component</th><th>Weight</th><th>Value</th><th>Contribution</th></tr>
        
                    <tr>
                        <td>Sortino Norm</td>
                        <td class="locked">25%</td>
                        <td>0.0000</td>
                        <td>0.0000</td>
                    </tr>
                
                    <tr>
                        <td>Ulcer Index Inv</td>
                        <td class="locked">20%</td>
                        <td>0.9943</td>
                        <td>0.1989</td>
                    </tr>
                
                    <tr>
                        <td>Equity Curve R2</td>
                        <td class="locked">15%</td>
                        <td>0.0352</td>
                        <td>0.0053</td>
                    </tr>
                
                    <tr>
                        <td>Profit Stability</td>
                        <td class="locked">15%</td>
                        <td>0.9607</td>
                        <td>0.1441</td>
                    </tr>
                
                    <tr>
                        <td>Upward Move Ratio</td>
                        <td class="locked">15%</td>
                        <td>0.4688</td>
                        <td>0.0703</td>
                    </tr>
                
                    <tr>
                        <td>Drawdown Duration Inv</td>
                        <td class="locked">10%</td>
                        <td>0.9009</td>
                        <td>0.0901</td>
                    </tr>
                
                    <tr style="background: #f39c12; color: white; font-weight: bold;">
                        <td><strong>🔒 TOTAL COMPOSITE SCORE</strong></td>
                        <td><strong>100%</strong></td>
                        <td><strong>0.5087</strong></td>
                        <td><strong>Target: ≥0.79</strong></td>
                    </tr>
            
                </table>
            </div>

            <div class="section">
                <h2>📊 ALL MODELS PERFORMANCE COMPARISON</h2>
                <table>
                    <tr>
                        <th>Model</th>
                        <th>Trades</th>
                        <th>Win Rate</th>
                        <th>Composite Score</th>
                        <th>Net Profit</th>
                        <th>Return %</th>
                        <th>Max DD</th>
                        <th>Status</th>
                    </tr>
        
                    <tr>
                        <td>_5</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5087</td>
                        <td>$-0.83</td>
                        <td>-0.28%</td>
                        <td>1.20%</td>
                        <td>🏆💰 BEST BOTH</td>
                    </tr>
            
                    <tr>
                        <td>_1</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#2</td>
                    </tr>
            
                    <tr>
                        <td>_2</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#3</td>
                    </tr>
            
                    <tr>
                        <td>_3</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#4</td>
                    </tr>
            
                    <tr>
                        <td>_4</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#5</td>
                    </tr>
            
                    <tr>
                        <td>_6</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#6</td>
                    </tr>
            
                    <tr>
                        <td>_7</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#7</td>
                    </tr>
            
                    <tr>
                        <td>_8</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#8</td>
                    </tr>
            
                    <tr>
                        <td>_9</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#9</td>
                    </tr>
            
                    <tr>
                        <td>10</td>
                        <td>21337</td>
                        <td>37.31%</td>
                        <td>0.5083</td>
                        <td>$-0.90</td>
                        <td>-0.30%</td>
                        <td>1.29%</td>
                        <td>#10</td>
                    </tr>
            
                </table>
            </div>

            <div class="section">
                <h2>🎯 TARGET VALIDATION</h2>
                <div class="metric">🎯 <strong>Win Rate Target:</strong> ≥90%
                    <span class="warning">
                        ⚠️ NEEDS OPTIMIZATION
                    </span>
                </div>
                <div class="metric">🔒 <strong>Composite Score Target:</strong> ≥0.79
                    <span class="warning">
                        ⚠️ NEEDS OPTIMIZATION
                    </span>
                </div>
                <div class="metric">💰 <strong>Net Profit:</strong> $-0.83
                    <span class="warning">
                        ⚠️ LOSS
                    </span>
                </div>
            </div>

            <div class="section">
                <h2>🔒 LOCKED SPECIFICATIONS CONFIRMED</h2>
                <div class="metric">🔒 <strong>Training Window:</strong> 60 days (LOCKED)</div>
                <div class="metric">🔒 <strong>Testing Window:</strong> 30 days (LOCKED)</div>
                <div class="metric">🔒 <strong>Grid Spacing:</strong> 0.125% (LOCKED)</div>
                <div class="metric">🔒 <strong>Risk/Reward:</strong> 2:1 ratio (LOCKED)</div>
                <div class="metric">🔒 <strong>Indicators:</strong> Exactly 4 (VWAP, BB, RSI, ETH/BTC)</div>
                <div class="metric">🔒 <strong>Models Saved:</strong> best_composite_score_model.json & best_net_profit_model.json</div>
            </div>
        </body>
        </html>
        