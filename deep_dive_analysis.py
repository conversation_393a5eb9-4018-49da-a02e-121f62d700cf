#!/usr/bin/env python3
"""
Deep Dive Analysis - Find exactly why training system has no profits
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json

class DeepDiveAnalysis:
    """Comprehensive analysis to find the root cause"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self.grid_spacing = 0.0025  # 0.25%
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for deep analysis")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def get_training_data(self, days: int = 5) -> pd.DataFrame:
        """Get sample training data"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} days of data for analysis...")
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data
            ohlcv = self.exchange.fetch_ohlcv(
                'BTC/USDT', 
                '1h',  # 1-hour timeframe
                since=since, 
                limit=days * 24
            )
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            print(f"✅ Collected {len(df)} candles")
            return df
            
        except Exception as e:
            print(f"❌ Error collecting data: {e}")
            return pd.DataFrame()
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate indicators exactly like training system"""
        print("📊 Calculating indicators...")
        
        # VWAP (20-period)
        try:
            df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
            df['vwap_ratio'] = df['vwap'] / df['close']
        except:
            df['vwap_ratio'] = 1.0
        
        # RSI (5-period)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (20-period)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ETH/BTC ratio (placeholder)
        df['eth_btc_ratio'] = 0.065
        
        # Price features
        df['price_change'] = df['close'].pct_change()
        df['volume_change'] = df['volume'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Grid features
        df['grid_spacing'] = 0.0025
        df['grid_level'] = np.round(df['close'] / (df['close'] * df['grid_spacing'])) * (df['close'] * df['grid_spacing'])
        df['grid_distance'] = np.abs(df['close'] - df['grid_level']) / (df['close'] * df['grid_spacing'])
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0.5)
        
        print(f"✅ Indicators calculated for {len(df)} candles")
        return df
    
    def analyze_label_generation(self, df: pd.DataFrame) -> Dict:
        """Analyze how labels are generated in training"""
        print("\n🔍 ANALYZING LABEL GENERATION")
        print("=" * 50)
        
        labels = []
        label_details = []
        
        sequence_length = 24
        
        for i in range(sequence_length, len(df)):
            current_price = df.iloc[i]['close']
            
            # Check grid proximity (training logic)
            grid_size = current_price * 0.0025
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity = abs(current_price - grid_level) / grid_level
            
            if grid_proximity > 0.0001:  # Not close enough to grid
                label = 2  # HOLD
                reason = f"Too far from grid ({grid_proximity:.6f})"
            else:
                # Look ahead for movement
                future_price = df.iloc[min(i+1, len(df)-1)]['close']
                price_change = (future_price - current_price) / current_price
                
                if price_change >= 0.0025:  # +0.25% grid-to-grid profit
                    label = 0  # BUY
                    reason = f"Future +0.25% move ({price_change:.4f})"
                elif price_change <= -0.0025:  # -0.25% grid-to-grid profit
                    label = 1  # SELL
                    reason = f"Future -0.25% move ({price_change:.4f})"
                else:
                    label = 2  # HOLD
                    reason = f"No clear movement ({price_change:.4f})"
            
            labels.append(label)
            label_details.append({
                'index': i,
                'price': current_price,
                'grid_level': grid_level,
                'grid_proximity': grid_proximity,
                'label': label,
                'reason': reason
            })
        
        # Analyze label distribution
        label_counts = {0: 0, 1: 0, 2: 0}  # BUY, SELL, HOLD
        for label in labels:
            label_counts[label] += 1
        
        total_labels = len(labels)
        
        analysis = {
            'total_sequences': total_labels,
            'buy_labels': label_counts[0],
            'sell_labels': label_counts[1], 
            'hold_labels': label_counts[2],
            'buy_percentage': (label_counts[0] / total_labels * 100) if total_labels > 0 else 0,
            'sell_percentage': (label_counts[1] / total_labels * 100) if total_labels > 0 else 0,
            'hold_percentage': (label_counts[2] / total_labels * 100) if total_labels > 0 else 0,
            'details': label_details[:10]  # First 10 for inspection
        }
        
        return analysis
    
    def analyze_trading_simulation(self, df: pd.DataFrame, predictions: np.ndarray, confidence: np.ndarray) -> Dict:
        """Analyze the trading simulation logic"""
        print("\n🔍 ANALYZING TRADING SIMULATION")
        print("=" * 50)
        
        balance = 300.0
        trades = []
        equity_curve = [balance]
        
        sequence_length = 24
        df_trading = df.iloc[sequence_length:]
        
        print(f"📊 Simulating {len(predictions)} predictions...")
        
        for i in range(len(predictions) - 1):
            if confidence[i] < 0.8:  # Skip low confidence
                equity_curve.append(balance)
                continue
            
            current_price = df_trading.iloc[i]['close']
            next_price = df_trading.iloc[i + 1]['close']
            
            risk_amount = 10.0
            
            if predictions[i] == 0:  # BUY
                price_change = (next_price - current_price) / current_price
                if price_change >= 0.0025:  # +0.25% take profit
                    profit = 20.0
                    exit_type = "TP"
                elif price_change <= -0.00125:  # -0.125% stop loss
                    profit = -10.0
                    exit_type = "SL"
                else:
                    profit = 0.0
                    exit_type = "NO_EXIT"
                
                balance += profit
                trades.append({
                    'direction': 'BUY',
                    'entry_price': current_price,
                    'exit_price': next_price,
                    'price_change': price_change,
                    'profit': profit,
                    'exit_type': exit_type,
                    'confidence': confidence[i]
                })
                
            elif predictions[i] == 1:  # SELL
                price_change = (current_price - next_price) / current_price
                if price_change >= 0.0025:  # +0.25% take profit
                    profit = 20.0
                    exit_type = "TP"
                elif price_change <= -0.00125:  # -0.125% stop loss
                    profit = -10.0
                    exit_type = "SL"
                else:
                    profit = 0.0
                    exit_type = "NO_EXIT"
                
                balance += profit
                trades.append({
                    'direction': 'SELL',
                    'entry_price': current_price,
                    'exit_price': next_price,
                    'price_change': price_change,
                    'profit': profit,
                    'exit_type': exit_type,
                    'confidence': confidence[i]
                })
            
            equity_curve.append(balance)
        
        # Analyze results
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['profit'] > 0])
        losing_trades = len([t for t in trades if t['profit'] < 0])
        no_exit_trades = len([t for t in trades if t['profit'] == 0])
        
        total_profit = sum(t['profit'] for t in trades)
        
        analysis = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'no_exit_trades': no_exit_trades,
            'win_rate': (winning_trades / total_trades * 100) if total_trades > 0 else 0,
            'total_profit': total_profit,
            'final_balance': balance,
            'return_pct': (balance - 300) / 300 * 100,
            'sample_trades': trades[:5]  # First 5 trades for inspection
        }
        
        return analysis
    
    def simulate_perfect_predictions(self, df: pd.DataFrame) -> Dict:
        """Simulate with perfect predictions to test the logic"""
        print("\n🔍 TESTING WITH PERFECT PREDICTIONS")
        print("=" * 50)
        
        # Create perfect predictions based on actual future movements
        sequence_length = 24
        df_trading = df.iloc[sequence_length:]
        
        perfect_predictions = []
        perfect_confidence = []
        
        for i in range(len(df_trading) - 1):
            current_price = df_trading.iloc[i]['close']
            next_price = df_trading.iloc[i + 1]['close']
            price_change = (next_price - current_price) / current_price
            
            # Check grid proximity
            grid_size = current_price * 0.0025
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity = abs(current_price - grid_level) / grid_level
            
            if grid_proximity > 0.0001:
                perfect_predictions.append(2)  # HOLD
                perfect_confidence.append(0.5)
            elif price_change >= 0.0025:  # Will hit TP for BUY
                perfect_predictions.append(0)  # BUY
                perfect_confidence.append(0.95)
            elif price_change <= -0.0025:  # Will hit TP for SELL
                perfect_predictions.append(1)  # SELL
                perfect_confidence.append(0.95)
            else:
                perfect_predictions.append(2)  # HOLD
                perfect_confidence.append(0.5)
        
        # Simulate with perfect predictions
        return self.analyze_trading_simulation(df, np.array(perfect_predictions), np.array(perfect_confidence))
    
    def run_deep_analysis(self):
        """Run comprehensive analysis"""
        print("🔍 STARTING DEEP DIVE ANALYSIS")
        print("=" * 60)
        
        # Get data
        df = self.get_training_data(5)
        if df.empty:
            return
        
        # Calculate indicators
        df = self.calculate_indicators(df)
        
        # 1. Analyze label generation
        label_analysis = self.analyze_label_generation(df)
        
        print(f"\n📊 LABEL ANALYSIS RESULTS:")
        print(f"   Total Sequences: {label_analysis['total_sequences']}")
        print(f"   BUY Labels: {label_analysis['buy_labels']} ({label_analysis['buy_percentage']:.1f}%)")
        print(f"   SELL Labels: {label_analysis['sell_labels']} ({label_analysis['sell_percentage']:.1f}%)")
        print(f"   HOLD Labels: {label_analysis['hold_labels']} ({label_analysis['hold_percentage']:.1f}%)")
        
        print(f"\n📋 SAMPLE LABEL DETAILS:")
        for detail in label_analysis['details'][:5]:
            print(f"   Price: ${detail['price']:,.2f} | Grid: ${detail['grid_level']:,.2f} | "
                  f"Proximity: {detail['grid_proximity']:.6f} | Label: {detail['label']} | {detail['reason']}")
        
        # 2. Test with perfect predictions
        perfect_analysis = self.simulate_perfect_predictions(df)
        
        print(f"\n🎯 PERFECT PREDICTION SIMULATION:")
        print(f"   Total Trades: {perfect_analysis['total_trades']}")
        print(f"   Winning Trades: {perfect_analysis['winning_trades']}")
        print(f"   Losing Trades: {perfect_analysis['losing_trades']}")
        print(f"   No Exit Trades: {perfect_analysis['no_exit_trades']}")
        print(f"   Win Rate: {perfect_analysis['win_rate']:.1f}%")
        print(f"   Total Profit: ${perfect_analysis['total_profit']:+.2f}")
        print(f"   Final Balance: ${perfect_analysis['final_balance']:,.2f}")
        print(f"   Return: {perfect_analysis['return_pct']:+.1f}%")
        
        print(f"\n📋 SAMPLE PERFECT TRADES:")
        for trade in perfect_analysis['sample_trades']:
            print(f"   {trade['direction']}: ${trade['entry_price']:,.2f} → ${trade['exit_price']:,.2f} | "
                  f"Change: {trade['price_change']:+.4f} | Profit: ${trade['profit']:+.2f} | {trade['exit_type']}")
        
        # 3. Identify issues
        print(f"\n🚨 ISSUE IDENTIFICATION:")
        
        if label_analysis['hold_percentage'] > 80:
            print(f"   ❌ ISSUE 1: Too many HOLD labels ({label_analysis['hold_percentage']:.1f}%)")
            print(f"      - Grid proximity threshold too strict (0.0001)")
            print(f"      - Consider relaxing to 0.001 (0.1%)")
        
        if perfect_analysis['no_exit_trades'] > perfect_analysis['winning_trades']:
            print(f"   ❌ ISSUE 2: Too many NO_EXIT trades ({perfect_analysis['no_exit_trades']})")
            print(f"      - 1-hour timeframe too short for 0.25% movements")
            print(f"      - Consider multi-hour exit checking")
        
        if perfect_analysis['total_profit'] <= 0:
            print(f"   ❌ ISSUE 3: Even perfect predictions not profitable")
            print(f"      - Logic error in profit calculation")
            print(f"      - Exit thresholds may be wrong")
        
        if label_analysis['buy_labels'] + label_analysis['sell_labels'] < 10:
            print(f"   ❌ ISSUE 4: Too few trading signals")
            print(f"      - Only {label_analysis['buy_labels'] + label_analysis['sell_labels']} trade signals")
            print(f"      - Need more relaxed conditions")

def main():
    """Run deep analysis"""
    analysis = DeepDiveAnalysis()
    analysis.run_deep_analysis()

if __name__ == "__main__":
    main()
