<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Bitcoin Freedom Live Trading</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Cache buster: {{ cache_buster }} -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px 0;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #00d4aa;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-running {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
        }

        .status-stopped {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff4444;
            color: #ff4444;
        }

        .status-live {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            color: #ffa500;
        }

        .status-sim {
            background: rgba(0, 191, 255, 0.2);
            border: 1px solid #00bfff;
            color: #00bfff;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .status-cog {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-cog:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
            border-color: #00d4aa;
        }

        .status-cog i {
            color: #ffffff;
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .status-cog:hover i {
            transform: rotate(90deg);
            color: #00d4aa;
        }

        .status-popup {
            position: fixed;
            bottom: 70px;
            left: 20px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00d4aa;
            border-radius: 10px;
            padding: 15px;
            min-width: 250px;
            max-width: 400px;
            font-family: monospace;
            font-size: 12px;
            color: #ffffff;
            z-index: 999;
            display: none;
            backdrop-filter: blur(10px);
        }

        .status-popup h4 {
            color: #00d4aa;
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .status-popup .status-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .status-popup .status-value {
            color: #00ff88;
        }

        .status-popup .status-error {
            color: #ff4444;
        }

        .status-popup .status-warning {
            color: #ffa500;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-start {
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
        }

        .btn-stop {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-toggle {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .price-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .price-value {
            font-size: 3rem;
            font-weight: bold;
            color: #00d4aa;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 5px;
        }

        .price-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #00d4aa;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric {
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: bold;
        }

        .profit {
            color: #00ff88;
        }

        .loss {
            color: #ff4444;
        }

        .trades-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        .trades-section h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #00d4aa;
        }

        .no-trades {
            text-align: center;
            padding: 40px;
            opacity: 0.6;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .price-value {
                font-size: 2.5rem;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 200px;
                justify-content: center;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-bitcoin"></i> Bitcoin Freedom Live Trading</h1>
        <p id="headerSubtitle">Loading...</p>
        <div class="status-bar">
            <div class="status-indicator status-stopped" id="tradingStatus">
                <i class="fas fa-circle"></i><span>Trading Stopped</span>
            </div>
            <div class="status-indicator status-sim" id="modeStatus">
                <i class="fas fa-flask"></i><span>Simulation Mode</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                <i class="fas fa-play"></i> Start Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                <i class="fas fa-stop"></i> Stop Trading
            </button>
            <button class="btn btn-toggle" id="toggleBtn" onclick="toggleMode()">
                <i class="fas fa-exchange-alt"></i> Switch to Live Mode
            </button>
        </div>
        
        <div class="price-display">
            <div class="price-value" id="currentPrice">$0</div>
            <div class="price-label">Bitcoin Price (Real-Time)</div>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> Trading Performance</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-label">Equity</div>
                        <div class="metric-value" id="equity">$0.00</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value" id="totalPnl">$0.00</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value" id="winRate">0%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Daily P&L</div>
                        <div class="metric-value" id="dailyPnl">$0.00</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3><i class="fas fa-cogs"></i> System Status</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-label">Open Positions</div>
                        <div class="metric-value" id="openPositions">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Daily Trades</div>
                        <div class="metric-value" id="dailyTrades">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Total Trades</div>
                        <div class="metric-value" id="totalTrades">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Model Score</div>
                        <div class="metric-value" id="compositeScore">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="trades-section">
            <h3><i class="fas fa-history"></i> Recent Trades</h3>
            <div id="tradesContainer">
                <div class="no-trades">No trades yet - start trading to see results</div>
            </div>
        </div>
    </div>
    <script>
        // Global state
        let state = {
            isRunning: false,
            isLiveMode: false,
            updateInterval: null
        };

        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                console.log(`🌐 API Call: ${endpoint}`);
                const response = await fetch(endpoint, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`✅ API Response: ${endpoint}`, data);
                return data;
            } catch (error) {
                console.error(`❌ API Error: ${endpoint}`, error);
                updateDebugInfo(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        // Update functions
        function updateDebugInfo(message) {
            // Debug info removed - status now shown in cog popup
            console.log(`Debug: ${message}`);
        }

        async function updateDashboard() {
            try {
                console.log('🔄 Updating dashboard...');
                const data = await apiCall('/api/trading_status');

                // Update debug info
                updateDebugInfo(`✅ Connected - Model: ${data.model_info.model_type} (${data.model_info.composite_score}%)`);

                // Update state
                state.isRunning = data.is_running;
                state.isLiveMode = data.is_live_mode;

                // Update header - remove TCN-CNN-PPO references
                let cleanModelType = data.model_info.model_type
                    .replace(/TCN-CNN-PPO/g, 'AI Model')
                    .replace(/Ensemble/g, '')
                    .replace(/AI Model AI/g, 'AI Model')
                    .replace(/\s+/g, ' ')
                    .trim();

                // Ensure we don't have duplicate "AI Model" text
                if (cleanModelType === 'AI Model AI' || cleanModelType === '') {
                    cleanModelType = 'AI Model';
                }

                document.getElementById('headerSubtitle').textContent =
                    `Production-Ready Auto Trading System | ${data.model_info.composite_score}% Composite Score | ${cleanModelType}`;

                // Update price
                document.getElementById('currentPrice').textContent = '$' + data.current_price.toLocaleString();

                // Update metrics
                document.getElementById('equity').textContent = '$' + data.performance.equity.toFixed(2);
                document.getElementById('totalPnl').textContent = '$' + data.performance.total_profit.toFixed(2);
                document.getElementById('winRate').textContent = data.performance.win_rate + '%';
                document.getElementById('dailyPnl').textContent = '$' + data.performance.daily_pnl.toFixed(2);
                document.getElementById('openPositions').textContent = data.performance.open_positions;
                document.getElementById('dailyTrades').textContent = data.performance.daily_trades;
                document.getElementById('totalTrades').textContent = data.performance.total_trades;
                document.getElementById('compositeScore').textContent = data.model_info.composite_score + '%';

                // Update colors
                updateColors();
                updateButtons();
                updateStatus();

                // Update trades
                await updateTrades();

            } catch (error) {
                console.error('❌ Dashboard update failed:', error);
                updateDebugInfo(`❌ Update failed: ${error.message}`);
            }
        }

        async function updateTrades() {
            try {
                const trades = await apiCall('/api/recent_trades');
                const container = document.getElementById('tradesContainer');

                if (trades.length === 0) {
                    container.innerHTML = '<div class="no-trades">No trades yet - start trading to see results</div>';
                    return;
                }

                container.innerHTML = trades.slice(-5).reverse().map(trade => `
                    <div style="background: rgba(0,0,0,0.2); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid ${trade.direction === 'BUY' ? '#00ff88' : '#ff6b6b'};">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                            <div>
                                <strong style="color: ${trade.direction === 'BUY' ? '#00ff88' : '#ff6b6b'};">${trade.direction}</strong>
                                <span style="opacity: 0.8; margin-left: 10px;">${trade.entry_time}</span>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: ${trade.pnl >= 0 ? '#00ff88' : '#ff4444'}; font-weight: bold;">
                                    $${trade.pnl.toFixed(2)} (${trade.profit_percentage.toFixed(2)}%)
                                </div>
                                <div style="opacity: 0.8; font-size: 0.9rem;">
                                    $${trade.entry_price.toLocaleString()} → ${trade.exit_price ? '$' + trade.exit_price.toLocaleString() : 'Open'}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('❌ Trades update failed:', error);
            }
        }

        function updateColors() {
            const totalPnl = parseFloat(document.getElementById('totalPnl').textContent.replace('$', ''));
            const dailyPnl = parseFloat(document.getElementById('dailyPnl').textContent.replace('$', ''));

            document.getElementById('totalPnl').className = 'metric-value ' + (totalPnl >= 0 ? 'profit' : 'loss');
            document.getElementById('dailyPnl').className = 'metric-value ' + (dailyPnl >= 0 ? 'profit' : 'loss');
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = state.isRunning;
            document.getElementById('stopBtn').disabled = !state.isRunning;
            document.getElementById('toggleBtn').disabled = state.isRunning;
            document.getElementById('toggleBtn').innerHTML = state.isLiveMode ?
                '<i class="fas fa-exchange-alt"></i> Switch to Simulation' :
                '<i class="fas fa-exchange-alt"></i> Switch to Live Mode';
        }

        function updateStatus() {
            const tradingStatus = document.getElementById('tradingStatus');
            const modeStatus = document.getElementById('modeStatus');

            if (state.isRunning) {
                tradingStatus.className = 'status-indicator status-running pulse';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Active</span>';
            } else {
                tradingStatus.className = 'status-indicator status-stopped';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Stopped</span>';
            }

            if (state.isLiveMode) {
                modeStatus.className = 'status-indicator status-live pulse';
                modeStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>LIVE MODE</span>';
            } else {
                modeStatus.className = 'status-indicator status-sim';
                modeStatus.innerHTML = '<i class="fas fa-flask"></i><span>Simulation Mode</span>';
            }
        }

        // Control functions
        async function startTrading() {
            try {
                const data = await apiCall('/api/start_trading', { method: 'POST' });
                if (data.status === 'success') {
                    state.isRunning = true;
                    updateButtons();
                    updateStatus();
                    alert('✅ Trading started successfully!');
                } else {
                    alert('❌ Error: ' + data.message);
                }
            } catch (error) {
                alert('❌ Failed to start trading: ' + error.message);
            }
        }

        async function stopTrading() {
            try {
                const data = await apiCall('/api/stop_trading', { method: 'POST' });
                if (data.status === 'success') {
                    state.isRunning = false;
                    updateButtons();
                    updateStatus();
                    alert('✅ Trading stopped successfully!');
                } else {
                    alert('❌ Error: ' + data.message);
                }
            } catch (error) {
                alert('❌ Failed to stop trading: ' + error.message);
            }
        }

        async function toggleMode() {
            const newMode = !state.isLiveMode;

            if (newMode) {
                const choice = prompt(
                    '⚠️ LIVE TRADING OPTIONS:\n\n' +
                    '1. TESTNET SPOT (Safe testing with fake money)\n' +
                    '2. LIVE SPOT (Real spot trading)\n' +
                    '3. LIVE CROSS MARGIN (Real margin trading)\n\n' +
                    'Enter 1, 2, or 3:'
                );

                if (choice === null) return;

                let testnet = true;
                let useMargin = false;
                let confirmMessage = '';

                if (choice === '3') {
                    testnet = false;
                    useMargin = true;
                    confirmMessage = '🚨 CROSS MARGIN TRADING!\n\nThis will use REAL MONEY with LEVERAGE.\nYou can lose more than your initial investment.\n\nAre you absolutely sure?';
                } else if (choice === '2') {
                    testnet = false;
                    useMargin = false;
                    confirmMessage = '🚨 LIVE SPOT TRADING!\n\nThis will place actual orders on Binance with your funds.\nNo leverage, but real money at risk.\n\nAre you absolutely sure?';
                } else if (choice === '1') {
                    testnet = true;
                    useMargin = false;
                    confirmMessage = '✅ TESTNET MODE: Safe testing with fake money.\n\nNo real money will be used. Continue?';
                } else {
                    alert('Invalid choice. Please enter 1, 2, or 3.');
                    return;
                }

                if (!confirm(confirmMessage)) return;

                try {
                    const data = await apiCall('/api/toggle_live_mode', {
                        method: 'POST',
                        body: JSON.stringify({
                            live_mode: newMode,
                            testnet: testnet,
                            use_margin: useMargin
                        })
                    });

                    if (data.status === 'success') {
                        state.isLiveMode = data.live_mode;
                        updateButtons();
                        updateStatus();
                        alert('✅ ' + data.message);
                    } else {
                        alert('❌ Error: ' + data.message);
                    }
                } catch (error) {
                    alert('❌ Failed to toggle mode: ' + error.message);
                }
            } else {
                try {
                    const data = await apiCall('/api/toggle_live_mode', {
                        method: 'POST',
                        body: JSON.stringify({ live_mode: false })
                    });

                    if (data.status === 'success') {
                        state.isLiveMode = data.live_mode;
                        updateButtons();
                        updateStatus();
                        alert('✅ ' + data.message);
                    } else {
                        alert('❌ Error: ' + data.message);
                    }
                } catch (error) {
                    alert('❌ Failed to toggle mode: ' + error.message);
                }
            }
        }

        // Initialize
        function startApp() {
            console.log('🚀 Bitcoin Freedom Trading Dashboard Starting...');

            // Clear any existing interval
            if (state.updateInterval) {
                clearInterval(state.updateInterval);
            }

            // Start updates
            updateDashboard();
            state.updateInterval = setInterval(updateDashboard, 3000); // Every 3 seconds

            console.log('✅ Dashboard initialized - updating every 3 seconds');
        }

        // Status cog functions
        function toggleStatusPopup() {
            const popup = document.getElementById('statusPopup');
            if (popup.style.display === 'none' || popup.style.display === '') {
                updateStatusPopup();
                popup.style.display = 'block';
            } else {
                popup.style.display = 'none';
            }
        }

        async function updateStatusPopup() {
            try {
                const data = await apiCall('/api/trading_status');
                const content = document.getElementById('statusContent');

                content.innerHTML = `
                    <div class="status-item">
                        <span>Model:</span>
                        <span class="status-value">${data.model_info.model_type.replace('TCN-CNN-PPO', 'AI Model')}</span>
                    </div>
                    <div class="status-item">
                        <span>Score:</span>
                        <span class="status-value">${data.model_info.composite_score}%</span>
                    </div>
                    <div class="status-item">
                        <span>Trading:</span>
                        <span class="${data.is_running ? 'status-value' : 'status-error'}">${data.is_running ? 'Active' : 'Stopped'}</span>
                    </div>
                    <div class="status-item">
                        <span>Mode:</span>
                        <span class="${data.is_live_mode ? 'status-warning' : 'status-value'}">${data.is_live_mode ? 'LIVE' : 'Simulation'}</span>
                    </div>
                    <div class="status-item">
                        <span>Price:</span>
                        <span class="status-value">$${data.current_price.toLocaleString()}</span>
                    </div>
                    <div class="status-item">
                        <span>P&L:</span>
                        <span class="${data.performance.total_profit >= 0 ? 'status-value' : 'status-error'}">$${data.performance.total_profit.toFixed(2)}</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('statusContent').innerHTML = `
                    <div class="status-item">
                        <span>Status:</span>
                        <span class="status-error">Connection Error</span>
                    </div>
                `;
            }
        }

        // Start when page loads
        document.addEventListener('DOMContentLoaded', startApp);
    </script>

    <!-- Status Cog -->
    <div class="status-cog" onclick="toggleStatusPopup()">
        <i class="fas fa-cog"></i>
    </div>

    <!-- Status Popup -->
    <div class="status-popup" id="statusPopup">
        <h4><i class="fas fa-info-circle"></i> System Status</h4>
        <div id="statusContent">Loading...</div>
    </div>

</body>
</html>
