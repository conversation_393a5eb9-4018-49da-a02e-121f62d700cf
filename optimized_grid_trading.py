#!/usr/bin/env python3
"""
🔒 OPTIMIZED GRID TRADING SYSTEM
Enhanced grid trading with proper 0.125% spacing and high-frequency trading
"""

import pandas as pd
import numpy as np
import ccxt
from datetime import datetime, timedelta
import logging
import json
import os
from typing import Dict, List, Tuple
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimized_grid_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OptimizedGridTradingSystem:
    """🔒 LOCKED: Optimized grid trading system with high-frequency execution"""
    
    def __init__(self):
        # 🔒 LOCKED PARAMETERS
        self.training_days = 60             # 🔒 LOCKED: 60 days training
        self.testing_days = 30              # 🔒 LOCKED: 30 days testing
        self.total_days = 90                # 🔒 LOCKED: 90 days total
        self.min_win_rate = 0.90            # 🔒 LOCKED: 90%+ target
        self.min_robust_score = 0.79        # 🔒 LOCKED: 0.79+ target
        
        # 🔒 LOCKED OPTIMIZED GRID PARAMETERS
        self.grid_spacing = 0.00125         # 🔒 LOCKED: 0.125% grid spacing
        self.take_profit_pct = 0.00125      # 🔒 LOCKED: 0.125% (1 grid level)
        self.stop_loss_pct = 0.000625       # 🔒 LOCKED: 0.0625% (0.5 grid level, 2:1 ratio)
        self.base_risk = 10.0               # 🔒 LOCKED: $10 per trade
        self.max_open_trades = 10           # 🔒 INCREASED: More concurrent positions
        self.confidence_threshold = 0.1     # 🔒 LOWERED: More aggressive trading
        
        # Initialize exchange
        self.exchange = None
        self._connect_exchange()
        
        # Trading state
        self.open_trades = []
        self.completed_trades = []
        self.last_trade_price = 0
        
    def _connect_exchange(self):
        """🔒 LOCKED: Connect to real Binance API"""
        try:
            with open('BinanceAPI_2.txt', 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            logging.info("🔒 REAL DATA: Connected to Binance API")
            
        except Exception as e:
            logging.error(f"🔒 CRITICAL: Failed to connect to Binance: {e}")
            raise
    
    def collect_real_data(self) -> pd.DataFrame:
        """🔒 LOCKED: Collect exactly 90 days of real Binance data"""
        logging.info("🔒 REAL DATA: Collecting 90 days of 1-minute BTC/USDT data...")
        
        try:
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(days=self.total_days)
            since = int(start_time.timestamp() * 1000)
            
            # Collect all data
            all_data = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                try:
                    ohlcv = self.exchange.fetch_ohlcv(
                        'BTC/USDT', '1m', since=current_since, limit=1000
                    )
                    
                    if not ohlcv:
                        break
                    
                    all_data.extend(ohlcv)
                    current_since = ohlcv[-1][0] + 60000  # Next minute
                    
                    # Rate limiting
                    time.sleep(0.05)  # Faster collection
                    
                    if len(all_data) % 20000 == 0:
                        logging.info(f"🔒 REAL DATA: Collected {len(all_data)} candles...")
                    
                except Exception as e:
                    logging.warning(f"Error fetching batch: {e}")
                    current_since += 3600000  # Skip 1 hour
                    continue
            
            # Create DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            df = df.drop_duplicates()
            df = df.sort_index()
            
            # Calculate locked indicators
            df = self._calculate_locked_indicators(df)
            
            logging.info(f"🔒 REAL DATA: Collected {len(df)} real data points")
            return df
            
        except Exception as e:
            logging.error(f"🔒 CRITICAL: Failed to collect real data: {e}")
            raise
    
    def _calculate_locked_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """🔒 LOCKED: Calculate exactly 4 indicators"""
        logging.info("🔒 INDICATORS: Calculating exactly 4 locked indicators...")
        
        # 🔒 INDICATOR 1: VWAP (20-period)
        df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / df['close']
        
        # 🔒 INDICATOR 2: Bollinger Bands Position (20-period, 2-std)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # 🔒 INDICATOR 3: RSI (14-period, normalized)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi_14'] = (100 - (100 / (1 + rs))) / 100.0  # Normalized 0-1
        
        # 🔒 INDICATOR 4: ETH/BTC Ratio
        try:
            eth_ticker = self.exchange.fetch_ticker('ETH/USDT')
            btc_ticker = self.exchange.fetch_ticker('BTC/USDT')
            eth_btc_ratio = eth_ticker['last'] / btc_ticker['last']
            df['eth_btc_ratio'] = eth_btc_ratio
            logging.info(f"🔒 ETH/BTC RATIO: {eth_btc_ratio:.6f}")
        except:
            df['eth_btc_ratio'] = 0.065  # Default
        
        # Remove NaN values
        df = df.dropna()
        
        logging.info("🔒 INDICATORS: All 4 locked indicators calculated")
        return df
    
    def split_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """🔒 LOCKED: Split into 60-day training and 30-day testing"""
        total_points = len(df)
        training_points = int(total_points * (self.training_days / self.total_days))
        
        train_data = df.iloc[:training_points].copy()
        test_data = df.iloc[training_points:].copy()
        
        logging.info(f"🔒 DATA SPLIT: Training={len(train_data)}, Testing={len(test_data)}")
        return train_data, test_data
    
    def get_enhanced_ml_signal(self, row) -> Tuple[float, float]:
        """🔒 LOCKED: Enhanced ML signal using 4 indicators"""
        # More sensitive signal generation
        vwap_signal = (row['vwap_ratio'] - 1.0) * 1000  # Amplified VWAP deviation
        bb_signal = (row['bb_position'] - 0.5) * 4      # Amplified BB position
        rsi_signal = (row['rsi_14'] - 0.5) * 4          # Amplified RSI momentum
        eth_btc_signal = (row['eth_btc_ratio'] - 0.065) * 1000  # Amplified ETH/BTC
        
        # Combined signal with equal weights
        signal = (vwap_signal * 0.25 + bb_signal * 0.25 + rsi_signal * 0.25 + eth_btc_signal * 0.25)
        
        # Normalize and calculate confidence
        normalized_signal = max(-1.0, min(1.0, signal / 5.0))
        confidence = min(1.0, abs(normalized_signal) + 0.2)  # Boost confidence
        
        return normalized_signal, confidence
    
    def should_trade_at_price(self, current_price: float) -> bool:
        """🔒 LOCKED: Check if price is near a grid level"""
        if self.last_trade_price == 0:
            return True  # First trade
        
        # Check if price moved enough from last trade (grid spacing)
        price_change = abs(current_price - self.last_trade_price) / self.last_trade_price
        return price_change >= self.grid_spacing * 0.5  # Half grid spacing for more trades
    
    def optimized_grid_backtest(self, test_data: pd.DataFrame) -> Dict:
        """🔒 LOCKED: Optimized grid trading backtest"""
        logging.info("🔒 OPTIMIZED GRID: Starting enhanced grid trading...")
        
        balance = 300.0  # Starting balance
        equity_curve = [balance]
        trade_count = 0
        
        # Enhanced grid trading simulation
        for i, (timestamp, row) in enumerate(test_data.iterrows()):
            current_price = row['close']
            
            # Get enhanced ML signal
            signal_strength, confidence = self.get_enhanced_ml_signal(row)
            
            # More aggressive trading conditions
            if (confidence > self.confidence_threshold and 
                len(self.open_trades) < self.max_open_trades and
                self.should_trade_at_price(current_price)):
                
                # Determine direction
                if signal_strength > 0.1:  # BUY signal
                    self._place_optimized_trade(timestamp, current_price, "BUY", confidence)
                    trade_count += 1
                elif signal_strength < -0.1:  # SELL signal
                    self._place_optimized_trade(timestamp, current_price, "SELL", confidence)
                    trade_count += 1
                
                self.last_trade_price = current_price
            
            # Check for trade exits
            self._check_trade_exits(timestamp, current_price)
            
            # Update equity curve more frequently
            if i % 50 == 0:
                unrealized_pnl = sum(self._calculate_unrealized_pnl(t, current_price) for t in self.open_trades)
                current_balance = balance + sum(t['pnl'] for t in self.completed_trades) + unrealized_pnl
                equity_curve.append(current_balance)
        
        # Close any remaining open trades
        final_price = test_data.iloc[-1]['close']
        for trade in self.open_trades:
            self._close_trade(trade, test_data.index[-1], final_price, "FINAL")
        
        # Calculate final results
        total_pnl = sum(t['pnl'] for t in self.completed_trades)
        final_balance = balance + total_pnl
        
        # Performance metrics
        winning_trades = [t for t in self.completed_trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(self.completed_trades) if self.completed_trades else 0
        
        # Enhanced robust score calculation
        if len(self.completed_trades) > 5:
            returns = [t['pnl'] / balance for t in self.completed_trades]
            avg_return = np.mean(returns)
            return_std = np.std(returns) if len(returns) > 1 else 0.01
            sharpe_like = avg_return / return_std if return_std > 0 else 0
            
            robust_score = min(1.0, max(0.0, 
                win_rate * 0.4 + 
                (total_pnl / balance) * 0.3 + 
                min(1.0, sharpe_like) * 0.3
            ))
        else:
            robust_score = 0.0
        
        results = {
            'total_trades': len(self.completed_trades),
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'final_balance': final_balance,
            'return_pct': (final_balance - balance) / balance * 100,
            'robust_score': robust_score,
            'trades': self.completed_trades,
            'equity_curve': equity_curve,
            'avg_trade_pnl': total_pnl / len(self.completed_trades) if self.completed_trades else 0
        }
        
        logging.info(f"🔒 OPTIMIZED GRID: Completed {len(self.completed_trades)} trades")
        logging.info(f"🔒 WIN RATE: {win_rate:.2%}")
        logging.info(f"🔒 ROBUST SCORE: {robust_score:.4f}")
        logging.info(f"🔒 AVG TRADE P&L: ${results['avg_trade_pnl']:.2f}")
        
        return results
    
    def _place_optimized_trade(self, timestamp, price: float, direction: str, confidence: float):
        """🔒 LOCKED: Place an optimized grid trade"""
        # Calculate exit levels with 2:1 risk/reward
        if direction == "BUY":
            take_profit = price * (1 + self.take_profit_pct)
            stop_loss = price * (1 - self.stop_loss_pct)
        else:
            take_profit = price * (1 - self.take_profit_pct)
            stop_loss = price * (1 + self.stop_loss_pct)
        
        # Dynamic position sizing based on confidence
        position_size = self.base_risk * (0.5 + confidence * 0.5)  # 50% to 100% of base risk
        
        trade = {
            'entry_time': timestamp,
            'direction': direction,
            'entry_price': price,
            'take_profit': take_profit,
            'stop_loss': stop_loss,
            'size': position_size,
            'confidence': confidence,
            'status': 'OPEN'
        }
        
        self.open_trades.append(trade)
    
    def _calculate_unrealized_pnl(self, trade: dict, current_price: float) -> float:
        """🔒 LOCKED: Calculate unrealized P&L"""
        if trade['direction'] == "BUY":
            return (current_price - trade['entry_price']) / trade['entry_price'] * trade['size']
        else:
            return (trade['entry_price'] - current_price) / trade['entry_price'] * trade['size']
    
    def _check_trade_exits(self, timestamp, current_price: float):
        """🔒 LOCKED: Check for trade exits"""
        trades_to_close = []
        
        for trade in self.open_trades:
            exit_triggered = False
            exit_reason = ""
            
            if trade['direction'] == "BUY":
                if current_price >= trade['take_profit']:
                    exit_triggered = True
                    exit_reason = "TP"
                elif current_price <= trade['stop_loss']:
                    exit_triggered = True
                    exit_reason = "SL"
            else:  # SELL
                if current_price <= trade['take_profit']:
                    exit_triggered = True
                    exit_reason = "TP"
                elif current_price >= trade['stop_loss']:
                    exit_triggered = True
                    exit_reason = "SL"
            
            if exit_triggered:
                self._close_trade(trade, timestamp, current_price, exit_reason)
                trades_to_close.append(trade)
        
        # Remove closed trades
        for trade in trades_to_close:
            self.open_trades.remove(trade)
    
    def _close_trade(self, trade: dict, timestamp, exit_price: float, exit_reason: str):
        """🔒 LOCKED: Close a trade and calculate P&L"""
        # Calculate P&L
        if trade['direction'] == "BUY":
            pnl = (exit_price - trade['entry_price']) / trade['entry_price'] * trade['size']
        else:
            pnl = (trade['entry_price'] - exit_price) / trade['entry_price'] * trade['size']
        
        trade.update({
            'exit_time': timestamp,
            'exit_price': exit_price,
            'exit_reason': exit_reason,
            'pnl': pnl,
            'status': 'CLOSED'
        })
        
        self.completed_trades.append(trade)
    
    def generate_enhanced_report(self, results: Dict) -> str:
        """🔒 LOCKED: Generate enhanced HTML report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>🔒 Optimized Grid Trading Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 10px; }}
                .section {{ background: white; margin: 20px 0; padding: 20px; border-radius: 10px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; background: #ecf0f1; border-radius: 5px; }}
                .success {{ color: #27ae60; font-weight: bold; }}
                .warning {{ color: #e74c3c; font-weight: bold; }}
                .locked {{ color: #8e44ad; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔒 OPTIMIZED GRID TRADING REPORT</h1>
                <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                <p class="locked">🔒 ENHANCED GRID SYSTEM - 2:1 Risk/Reward</p>
            </div>
            
            <div class="section">
                <h2>📊 ENHANCED PERFORMANCE RESULTS</h2>
                <div class="metric">📈 <strong>Total Trades:</strong> {results['total_trades']}</div>
                <div class="metric">🎯 <strong>Win Rate:</strong> {results['win_rate']:.2%}</div>
                <div class="metric">💰 <strong>Total P&L:</strong> ${results['total_pnl']:.2f}</div>
                <div class="metric">📊 <strong>Return:</strong> {results['return_pct']:.2f}%</div>
                <div class="metric">🔒 <strong>Robust Score:</strong> {results['robust_score']:.4f}</div>
                <div class="metric">💵 <strong>Avg Trade P&L:</strong> ${results['avg_trade_pnl']:.2f}</div>
            </div>
            
            <div class="section">
                <h2>🔒 GRID TRADING SPECIFICATIONS</h2>
                <div class="metric">🔒 <strong>Grid Spacing:</strong> 0.125% (LOCKED)</div>
                <div class="metric">🔒 <strong>Take Profit:</strong> 0.125% (LOCKED)</div>
                <div class="metric">🔒 <strong>Stop Loss:</strong> 0.0625% (LOCKED)</div>
                <div class="metric">🔒 <strong>Risk/Reward:</strong> 2:1 (LOCKED)</div>
                <div class="metric">🔒 <strong>Max Positions:</strong> 10 (OPTIMIZED)</div>
            </div>
            
            <div class="section">
                <h2>🎯 TARGET VALIDATION</h2>
                <div class="metric">🎯 <strong>Win Rate Target:</strong> ≥90% 
                    <span class="{'success' if results['win_rate'] >= 0.90 else 'warning'}">
                        {'✅ ACHIEVED' if results['win_rate'] >= 0.90 else '⚠️ NEEDS OPTIMIZATION'}
                    </span>
                </div>
                <div class="metric">📊 <strong>Robust Score Target:</strong> ≥0.79 
                    <span class="{'success' if results['robust_score'] >= 0.79 else 'warning'}">
                        {'✅ ACHIEVED' if results['robust_score'] >= 0.79 else '⚠️ NEEDS OPTIMIZATION'}
                    </span>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Save report
        os.makedirs('html_reports', exist_ok=True)
        report_path = f"html_reports/optimized_grid_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logging.info(f"🔒 REPORT: Generated {report_path}")
        return report_path
    
    def run_complete_training(self):
        """🔒 LOCKED: Execute complete optimized grid trading pipeline"""
        logging.info("🚀 STARTING OPTIMIZED GRID TRADING SYSTEM")
        logging.info("=" * 70)
        
        try:
            # Step 1: Collect real data
            df = self.collect_real_data()
            
            # Step 2: Split data
            train_data, test_data = self.split_data(df)
            
            # Step 3: Optimized grid trading backtest
            validation_results = self.optimized_grid_backtest(test_data)
            
            # Step 4: Generate enhanced report
            report_path = self.generate_enhanced_report(validation_results)
            
            # Final summary
            logging.info("🎉 OPTIMIZED GRID TRADING COMPLETED!")
            logging.info(f"📊 Win Rate: {validation_results['win_rate']:.2%}")
            logging.info(f"🔒 Robust Score: {validation_results['robust_score']:.4f}")
            logging.info(f"📈 Return: {validation_results['return_pct']:.2f}%")
            logging.info(f"📝 Report: {report_path}")
            
            return validation_results
            
        except Exception as e:
            logging.error(f"❌ OPTIMIZED GRID TRADING FAILED: {e}")
            raise

if __name__ == "__main__":
    trader = OptimizedGridTradingSystem()
    results = trader.run_complete_training()
