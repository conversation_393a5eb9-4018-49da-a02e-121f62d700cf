#!/usr/bin/env python3
"""
Test script to verify the trading system matches the ORIGINAL PLAN
"""

import requests
import json
import sys

def test_api_endpoint(endpoint, expected_values=None):
    """Test an API endpoint and verify expected values"""
    try:
        response = requests.get(f'http://localhost:5000{endpoint}')
        data = response.json()
        
        print(f"\n🔍 Testing {endpoint}")
        print(f"✅ Response: {json.dumps(data, indent=2)}")
        
        if expected_values:
            for key, expected in expected_values.items():
                actual = data.get(key)
                if actual == expected:
                    print(f"✅ {key}: {actual} (CORRECT)")
                else:
                    print(f"❌ {key}: {actual} (EXPECTED: {expected})")
                    return False
        return True
    except Exception as e:
        print(f"❌ Error testing {endpoint}: {e}")
        return False

def main():
    """Run comprehensive compliance tests"""
    print("🎯 TESTING ORIGINAL PLAN COMPLIANCE")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Risk Management Parameters (UPDATED SPECIFICATIONS)
    print("\n📊 TEST 1: RISK MANAGEMENT PARAMETERS")
    risk_expected = {
        'risk_scaling': 'Dynamic based on account size',
        'current_risk_amount': '$10.00',
        'risk_formula': '$10 up to $1000, then +$10 per $500',
        'take_profit': '0.25%',
        'stop_loss': '0.125%',
        'risk_reward_ratio': '1:2',
        'starting_balance': '$300',
        'grid_spacing': '0.25%',
        'indicators': 'VWAP, RSI(5), Bollinger Bands, ETH/BTC',
        'data_timeframe': '1-minute',
        'training_period': '60 days',
        'testing_period': '30 days',
        'min_win_rate': '93%',
        'min_composite_score': '79'
    }
    
    if not test_api_endpoint('/api/risk_info', risk_expected):
        all_tests_passed = False
    
    # Test 2: System Status
    print("\n📊 TEST 2: SYSTEM STATUS")
    if not test_api_endpoint('/api/system_status'):
        all_tests_passed = False
    
    # Test 3: Signal Generation
    print("\n📊 TEST 3: SIGNAL GENERATION")
    if not test_api_endpoint('/api/signal_status'):
        all_tests_passed = False
    
    # Test 4: Market Data
    print("\n📊 TEST 4: MARKET DATA")
    if not test_api_endpoint('/api/market_data'):
        all_tests_passed = False
    
    # Test 5: Portfolio Data
    print("\n📊 TEST 5: PORTFOLIO DATA")
    if not test_api_endpoint('/api/portfolio_data'):
        all_tests_passed = False
    
    # Final Results
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - SYSTEM MATCHES UPDATED SPECIFICATIONS!")
        print("✅ Risk Management: Dynamic scaling ($10 up to $1000, then +$10 per $500)")
        print("✅ Risk/Reward Ratio: 1:2 (0.25% TP, 0.125% SL)")
        print("✅ Indicators: VWAP, RSI(5), Bollinger Bands, ETH/BTC")
        print("✅ Grid Spacing: 0.25% (MAINTAINED)")
        print("✅ Data Timeframe: 1-minute (UPDATED)")
        print("✅ Training: 60 days, Testing: 30 days (UPDATED)")
        print("✅ Model Thresholds: 93% win rate, 79 composite score (NEW)")
        print("✅ Starting Balance: $300 (MAINTAINED)")
        return 0
    else:
        print("❌ SOME TESTS FAILED - SYSTEM NEEDS FURTHER CORRECTION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
