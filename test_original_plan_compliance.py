#!/usr/bin/env python3
"""
Test script to verify the trading system matches the ORIGINAL PLAN
"""

import requests
import json
import sys

def test_api_endpoint(endpoint, expected_values=None):
    """Test an API endpoint and verify expected values"""
    try:
        response = requests.get(f'http://localhost:5000{endpoint}')
        data = response.json()
        
        print(f"\n🔍 Testing {endpoint}")
        print(f"✅ Response: {json.dumps(data, indent=2)}")
        
        if expected_values:
            for key, expected in expected_values.items():
                actual = data.get(key)
                if actual == expected:
                    print(f"✅ {key}: {actual} (CORRECT)")
                else:
                    print(f"❌ {key}: {actual} (EXPECTED: {expected})")
                    return False
        return True
    except Exception as e:
        print(f"❌ Error testing {endpoint}: {e}")
        return False

def main():
    """Run comprehensive compliance tests"""
    print("🎯 TESTING ORIGINAL PLAN COMPLIANCE")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Risk Management Parameters
    print("\n📊 TEST 1: RISK MANAGEMENT PARAMETERS")
    risk_expected = {
        'risk_per_trade': '5% of equity',
        'take_profit': '0.25%',
        'stop_loss': '0.125%',
        'risk_reward_ratio': '1:2',
        'starting_balance': '$300',
        'grid_spacing': '0.25%',
        'indicators': 'VWAP, RSI(5), Bollinger Bands, ETH/BTC'
    }
    
    if not test_api_endpoint('/api/risk_info', risk_expected):
        all_tests_passed = False
    
    # Test 2: System Status
    print("\n📊 TEST 2: SYSTEM STATUS")
    if not test_api_endpoint('/api/system_status'):
        all_tests_passed = False
    
    # Test 3: Signal Generation
    print("\n📊 TEST 3: SIGNAL GENERATION")
    if not test_api_endpoint('/api/signal_status'):
        all_tests_passed = False
    
    # Test 4: Market Data
    print("\n📊 TEST 4: MARKET DATA")
    if not test_api_endpoint('/api/market_data'):
        all_tests_passed = False
    
    # Test 5: Portfolio Data
    print("\n📊 TEST 5: PORTFOLIO DATA")
    if not test_api_endpoint('/api/portfolio_data'):
        all_tests_passed = False
    
    # Final Results
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - SYSTEM MATCHES ORIGINAL PLAN!")
        print("✅ Risk Management: 5% equity, 0.25% TP, 0.125% SL")
        print("✅ Risk/Reward Ratio: 1:2 (CORRECTED)")
        print("✅ Indicators: VWAP, RSI(5), Bollinger Bands, ETH/BTC")
        print("✅ Grid Spacing: 0.25% (CORRECT)")
        print("✅ Starting Balance: $300 (CORRECT)")
        return 0
    else:
        print("❌ SOME TESTS FAILED - SYSTEM NEEDS FURTHER CORRECTION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
