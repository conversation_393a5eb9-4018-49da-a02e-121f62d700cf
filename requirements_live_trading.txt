# Conservative Elite Live Trading System Requirements
# Install with: pip install -r requirements_live_trading.txt

# Core trading dependencies
ccxt>=4.0.0
pandas>=1.5.0
numpy>=1.24.0

# Web application
Flask>=2.3.0

# Data processing
scikit-learn>=1.3.0

# Logging and utilities
python-dateutil>=2.8.0

# Optional: For enhanced performance
numba>=0.57.0

# Development dependencies (optional)
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
