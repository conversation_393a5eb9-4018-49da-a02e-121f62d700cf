#!/usr/bin/env python3
"""
Grid Spacing Comparison Test
Compare different grid spacing configurations to find optimal settings
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json

class GridSpacingComparison:
    """Compare different grid spacing configurations"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            print("✅ Connected to Binance for comparison test")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
    
    def get_test_data(self, days: int = 7) -> pd.DataFrame:
        """Get test data"""
        if not self.exchange:
            return pd.DataFrame()
        
        print(f"📊 Collecting {days} days of test data...")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        since = int(start_time.timestamp() * 1000)
        
        try:
            ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1h', since=since, limit=days * 24)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            # Calculate indicators
            df = self.calculate_indicators(df)
            
            print(f"✅ Collected {len(df)} candles")
            return df
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return pd.DataFrame()
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # VWAP (20-period)
        df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / df['close']
        
        # RSI (5-period)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (20-period)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0.5)
        
        return df
    
    def test_grid_configuration(self, df: pd.DataFrame, config: Dict) -> Dict:
        """Test a specific grid configuration"""
        grid_spacing = config['grid_spacing']
        take_profit_pct = config['take_profit_pct']
        stop_loss_pct = config['stop_loss_pct']
        proximity_threshold = config['proximity_threshold']
        
        balance = 300.0
        trades = []
        
        for i, (timestamp, row) in enumerate(df.iterrows()):
            current_price = row['close']
            
            # Check if indicators are available
            if pd.isna(row['vwap_ratio']) or pd.isna(row['rsi_norm']) or pd.isna(row['bb_position']):
                continue
            
            # Grid proximity check
            grid_size = current_price * grid_spacing
            grid_level = round(current_price / grid_size) * grid_size
            grid_proximity_ratio = abs(current_price - grid_level) / grid_level
            
            if grid_proximity_ratio > proximity_threshold:
                continue
            
            # Technical conditions
            vwap_ratio = row['vwap_ratio']
            rsi = row['rsi_norm']
            bb_position = row['bb_position']
            
            buy_signal = (rsi < 0.4 and bb_position < 0.4 and vwap_ratio > 0.998)
            sell_signal = (rsi > 0.6 and bb_position > 0.6 and vwap_ratio < 1.002)
            
            if not (buy_signal or sell_signal):
                continue
            
            direction = "BUY" if buy_signal else "SELL"
            
            # Look ahead for exits (next 6 hours)
            profit = 0.0
            exit_type = "NO_EXIT"
            
            for j in range(1, min(7, len(df) - i)):
                future_price = df.iloc[i + j]['close']
                
                if direction == "BUY":
                    price_change = (future_price - current_price) / current_price
                    if price_change >= take_profit_pct:
                        profit = 20.0  # $20 profit (2:1 ratio)
                        exit_type = "TP"
                        break
                    elif price_change <= -stop_loss_pct:
                        profit = -10.0  # $10 loss
                        exit_type = "SL"
                        break
                
                else:  # SELL
                    price_change = (current_price - future_price) / current_price
                    if price_change >= take_profit_pct:
                        profit = 20.0  # $20 profit (2:1 ratio)
                        exit_type = "TP"
                        break
                    elif price_change <= -stop_loss_pct:
                        profit = -10.0  # $10 loss
                        exit_type = "SL"
                        break
            
            if profit != 0:  # Only count trades that exit
                balance += profit
                trades.append({
                    'direction': direction,
                    'entry_price': current_price,
                    'profit': profit,
                    'exit_type': exit_type,
                    'grid_proximity': grid_proximity_ratio
                })
        
        # Calculate results
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['profit'] > 0])
        total_profit = sum(t['profit'] for t in trades)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'config': config,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'final_balance': balance,
            'return_pct': (balance - 300) / 300 * 100,
            'avg_profit_per_trade': total_profit / total_trades if total_trades > 0 else 0
        }
    
    def run_comparison(self):
        """Run comparison of different grid configurations"""
        print("🔍 GRID SPACING CONFIGURATION COMPARISON")
        print("=" * 60)
        
        # Get test data
        df = self.get_test_data(7)
        if df.empty:
            return
        
        # Test configurations
        configurations = [
            {
                'name': 'Current (0.25% grid, 0.25% TP, 0.125% SL)',
                'grid_spacing': 0.0025,
                'take_profit_pct': 0.0025,
                'stop_loss_pct': 0.00125,
                'proximity_threshold': 0.0001
            },
            {
                'name': 'Alternative 1 (0.125% grid, 0.25% TP, 0.125% SL)',
                'grid_spacing': 0.00125,
                'take_profit_pct': 0.0025,
                'stop_loss_pct': 0.00125,
                'proximity_threshold': 0.0001
            },
            {
                'name': 'Alternative 2 (0.125% grid, 0.125% TP, 0.0625% SL)',
                'grid_spacing': 0.00125,
                'take_profit_pct': 0.00125,
                'stop_loss_pct': 0.000625,
                'proximity_threshold': 0.0001
            },
            {
                'name': 'Alternative 3 (0.25% grid, 0.5% TP, 0.25% SL)',
                'grid_spacing': 0.0025,
                'take_profit_pct': 0.005,
                'stop_loss_pct': 0.0025,
                'proximity_threshold': 0.0001
            },
            {
                'name': 'Relaxed Proximity (0.25% grid, 0.25% TP, 0.125% SL, 0.1% prox)',
                'grid_spacing': 0.0025,
                'take_profit_pct': 0.0025,
                'stop_loss_pct': 0.00125,
                'proximity_threshold': 0.001
            }
        ]
        
        results = []
        
        print(f"\n📊 Testing {len(configurations)} configurations on {len(df)} candles...")
        print("-" * 60)
        
        for config in configurations:
            print(f"\n🧪 Testing: {config['name']}")
            result = self.test_grid_configuration(df, config)
            results.append(result)
            
            print(f"   Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Total Profit: ${result['total_profit']:+.2f}")
            print(f"   Return: {result['return_pct']:+.1f}%")
            print(f"   Avg Profit/Trade: ${result['avg_profit_per_trade']:+.2f}")
        
        # Find best configuration
        print(f"\n🏆 COMPARISON RESULTS:")
        print("=" * 60)
        
        # Sort by total profit
        results_by_profit = sorted(results, key=lambda x: x['total_profit'], reverse=True)
        
        print(f"\n💰 RANKED BY TOTAL PROFIT:")
        for i, result in enumerate(results_by_profit, 1):
            config = result['config']
            print(f"{i}. {config['name']}")
            print(f"   Grid: {config['grid_spacing']*100:.3f}% | TP: {config['take_profit_pct']*100:.3f}% | SL: {config['stop_loss_pct']*100:.3f}%")
            print(f"   Trades: {result['total_trades']} | Win Rate: {result['win_rate']:.1f}% | Profit: ${result['total_profit']:+.2f} | Return: {result['return_pct']:+.1f}%")
            print()
        
        # Sort by win rate
        results_by_winrate = sorted(results, key=lambda x: x['win_rate'], reverse=True)
        
        print(f"🎯 RANKED BY WIN RATE:")
        for i, result in enumerate(results_by_winrate, 1):
            config = result['config']
            print(f"{i}. {config['name']}")
            print(f"   Win Rate: {result['win_rate']:.1f}% | Trades: {result['total_trades']} | Profit: ${result['total_profit']:+.2f}")
            print()
        
        # Sort by return percentage
        results_by_return = sorted(results, key=lambda x: x['return_pct'], reverse=True)
        
        print(f"📈 RANKED BY RETURN %:")
        for i, result in enumerate(results_by_return, 1):
            config = result['config']
            print(f"{i}. {config['name']}")
            print(f"   Return: {result['return_pct']:+.1f}% | Profit: ${result['total_profit']:+.2f} | Win Rate: {result['win_rate']:.1f}%")
            print()
        
        # Recommendation
        best_overall = results_by_profit[0]
        print(f"🚀 RECOMMENDATION:")
        print(f"Best Overall Performance: {best_overall['config']['name']}")
        print(f"   Grid Spacing: {best_overall['config']['grid_spacing']*100:.3f}%")
        print(f"   Take Profit: {best_overall['config']['take_profit_pct']*100:.3f}%")
        print(f"   Stop Loss: {best_overall['config']['stop_loss_pct']*100:.3f}%")
        print(f"   Proximity Threshold: {best_overall['config']['proximity_threshold']*100:.4f}%")
        print(f"   Expected Performance: {best_overall['total_trades']} trades, {best_overall['win_rate']:.1f}% win rate, ${best_overall['total_profit']:+.2f} profit")

def main():
    """Run grid spacing comparison"""
    comparison = GridSpacingComparison()
    comparison.run_comparison()

if __name__ == "__main__":
    main()
