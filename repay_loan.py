#!/usr/bin/env python3
"""
Simple script to trigger loan repayment via API
"""

import requests
import json

def repay_loan():
    """Trigger loan repayment via POST request"""
    try:
        url = "http://localhost:5000/api/repay_loan"
        
        print("🏦 Triggering loan repayment...")
        response = requests.post(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Loan repayment response:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error triggering loan repayment: {e}")

if __name__ == "__main__":
    repay_loan()
