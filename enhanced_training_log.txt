2025-06-07 18:47:06,488 - INFO - 🔒 REAL DATA: Connected to Binance API
2025-06-07 18:47:06,489 - INFO - 🚀 STARTING ENHANCED TRAINING PIPELINE - 60 DAY TRAINING / 30 DAY TESTING
2025-06-07 18:47:06,490 - INFO - ==========================================================================================
2025-06-07 18:47:06,490 - INFO - 📊 STEP 1: Collecting 90 days of real Binance data...
2025-06-07 18:47:06,490 - INFO - 🔒 REAL DATA: Collecting 90 days of 1-minute BTC/USDT data...
2025-06-07 18:47:18,903 - INFO - 🔒 REAL DATA: Collected 25000 candles...
2025-06-07 18:47:27,686 - INFO - 🔒 REAL DATA: Collected 50000 candles...
2025-06-07 18:47:36,397 - INFO - 🔒 REAL DATA: Collected 75000 candles...
2025-06-07 18:47:47,610 - INFO - 🔒 REAL DATA: Collected 100000 candles...
2025-06-07 18:47:56,619 - INFO - 🔒 REAL DATA: Collected 125000 candles...
2025-06-07 18:47:58,423 - INFO - 🔒 INDICATORS: Calculating exactly 4 locked indicators...
2025-06-07 18:47:58,944 - INFO - 🔒 ETH/BTC RATIO: 0.023866
2025-06-07 18:47:58,983 - INFO - 🔒 INDICATORS: All 4 locked indicators calculated
2025-06-07 18:47:58,992 - INFO - 🔒 REAL DATA: Collected 129521 real data points
2025-06-07 18:47:59,013 - INFO - ✂️ STEP 2: Splitting data - 60 days training, 30 days testing...
2025-06-07 18:47:59,073 - INFO - 🔒 DATA SPLIT: Training=86347 (60 days), Testing=43174 (30 days)
2025-06-07 18:47:59,074 - INFO - 🧠 STEP 3: Training 10 enhanced models with composite score optimization...
2025-06-07 18:47:59,075 - INFO - 🔒 ENHANCED TRAINING: Creating and testing 10 optimized models...
2025-06-07 18:47:59,075 - INFO - 🔒 MODEL 1/10: Training enhanced model...
2025-06-07 18:47:59,075 - INFO - 🔒 ML TRAINING: Creating optimized model 1...
2025-06-07 18:47:59,076 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:47:59,076 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:47:59,076 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:47:59,077 - INFO - 🔒 ML TRAINING: Model 1 training completed
2025-06-07 18:47:59,078 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184759_1 on 30-day out-of-sample data...
2025-06-07 18:48:11,080 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:48:15,147 - INFO - 🏆 NEW BEST COMPOSITE SCORE: 0.5083 - Saved to best_composite_score_model.json
2025-06-07 18:48:17,486 - INFO - 💰 NEW BEST NET PROFIT: $-0.90 - Saved to best_net_profit_model.json
2025-06-07 18:48:17,490 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184759_1.json
2025-06-07 18:48:17,493 - INFO - 🔒 MODEL 1: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:48:17,494 - INFO - 🔒 MODEL 2/10: Training enhanced model...
2025-06-07 18:48:17,494 - INFO - 🔒 ML TRAINING: Creating optimized model 2...
2025-06-07 18:48:17,499 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:48:17,505 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:48:17,508 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:48:17,509 - INFO - 🔒 ML TRAINING: Model 2 training completed
2025-06-07 18:48:17,509 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184817_2 on 30-day out-of-sample data...
2025-06-07 18:48:29,437 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:48:31,630 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184817_2.json
2025-06-07 18:48:31,632 - INFO - 🔒 MODEL 2: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:48:31,637 - INFO - 🔒 MODEL 3/10: Training enhanced model...
2025-06-07 18:48:31,643 - INFO - 🔒 ML TRAINING: Creating optimized model 3...
2025-06-07 18:48:31,646 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:48:31,649 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:48:31,651 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:48:31,866 - INFO - 🔒 ML TRAINING: Model 3 training completed
2025-06-07 18:48:31,926 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184831_3 on 30-day out-of-sample data...
2025-06-07 18:48:42,661 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:48:44,707 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184831_3.json
2025-06-07 18:48:44,708 - INFO - 🔒 MODEL 3: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:48:44,709 - INFO - 🔒 MODEL 4/10: Training enhanced model...
2025-06-07 18:48:44,709 - INFO - 🔒 ML TRAINING: Creating optimized model 4...
2025-06-07 18:48:44,710 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:48:44,710 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:48:44,710 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:48:44,711 - INFO - 🔒 ML TRAINING: Model 4 training completed
2025-06-07 18:48:44,711 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184844_4 on 30-day out-of-sample data...
2025-06-07 18:48:56,405 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:48:58,438 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184844_4.json
2025-06-07 18:48:58,439 - INFO - 🔒 MODEL 4: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:48:58,439 - INFO - 🔒 MODEL 5/10: Training enhanced model...
2025-06-07 18:48:58,440 - INFO - 🔒 ML TRAINING: Creating optimized model 5...
2025-06-07 18:48:58,440 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:48:58,441 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:48:58,442 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:48:58,442 - INFO - 🔒 ML TRAINING: Model 5 training completed
2025-06-07 18:48:58,443 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184858_5 on 30-day out-of-sample data...
2025-06-07 18:49:17,105 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5087 composite score, $-0.83 net profit
2025-06-07 18:49:21,744 - INFO - 🏆 NEW BEST COMPOSITE SCORE: 0.5087 - Saved to best_composite_score_model.json
2025-06-07 18:49:24,196 - INFO - 💰 NEW BEST NET PROFIT: $-0.83 - Saved to best_net_profit_model.json
2025-06-07 18:49:24,200 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184858_5.json
2025-06-07 18:49:24,202 - INFO - 🔒 MODEL 5: Win Rate=37.31%, Composite Score=0.5087, Net Profit=$-0.83
2025-06-07 18:49:24,203 - INFO - 🔒 MODEL 6/10: Training enhanced model...
2025-06-07 18:49:24,203 - INFO - 🔒 ML TRAINING: Creating optimized model 6...
2025-06-07 18:49:24,205 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:49:24,207 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:49:24,330 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:49:24,375 - INFO - 🔒 ML TRAINING: Model 6 training completed
2025-06-07 18:49:24,394 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184924_6 on 30-day out-of-sample data...
2025-06-07 18:49:37,042 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:49:39,274 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184924_6.json
2025-06-07 18:49:39,279 - INFO - 🔒 MODEL 6: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:49:39,280 - INFO - 🔒 MODEL 7/10: Training enhanced model...
2025-06-07 18:49:39,280 - INFO - 🔒 ML TRAINING: Creating optimized model 7...
2025-06-07 18:49:39,281 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:49:39,282 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:49:39,285 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:49:39,286 - INFO - 🔒 ML TRAINING: Model 7 training completed
2025-06-07 18:49:39,286 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184939_7 on 30-day out-of-sample data...
2025-06-07 18:49:52,267 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:49:54,590 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184939_7.json
2025-06-07 18:49:54,594 - INFO - 🔒 MODEL 7: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:49:54,597 - INFO - 🔒 MODEL 8/10: Training enhanced model...
2025-06-07 18:49:54,606 - INFO - 🔒 ML TRAINING: Creating optimized model 8...
2025-06-07 18:49:54,608 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:49:54,609 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:49:54,615 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:49:54,618 - INFO - 🔒 ML TRAINING: Model 8 training completed
2025-06-07 18:49:54,620 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_184954_8 on 30-day out-of-sample data...
2025-06-07 18:50:07,236 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:50:09,922 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_184954_8.json
2025-06-07 18:50:09,923 - INFO - 🔒 MODEL 8: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:50:09,924 - INFO - 🔒 MODEL 9/10: Training enhanced model...
2025-06-07 18:50:09,924 - INFO - 🔒 ML TRAINING: Creating optimized model 9...
2025-06-07 18:50:09,925 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:50:09,926 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:50:09,928 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:50:09,930 - INFO - 🔒 ML TRAINING: Model 9 training completed
2025-06-07 18:50:09,931 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_185009_9 on 30-day out-of-sample data...
2025-06-07 18:50:23,599 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:50:25,903 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_185009_9.json
2025-06-07 18:50:25,908 - INFO - 🔒 MODEL 9: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:50:25,910 - INFO - 🔒 MODEL 10/10: Training enhanced model...
2025-06-07 18:50:25,921 - INFO - 🔒 ML TRAINING: Creating optimized model 10...
2025-06-07 18:50:25,925 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 18:50:25,930 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 18:50:25,946 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 18:50:25,951 - INFO - 🔒 ML TRAINING: Model 10 training completed
2025-06-07 18:50:25,956 - INFO - 🔒 BACKTESTING: Testing model enhanced_tcn_cnn_ppo_20250607_185025_10 on 30-day out-of-sample data...
2025-06-07 18:50:42,465 - INFO - 🔒 BACKTESTING: 21337 trades, 37.31% win rate, 0.5083 composite score, $-0.90 net profit
2025-06-07 18:50:45,422 - INFO - 🔒 MODEL SAVED: best_models\model_enhanced_tcn_cnn_ppo_20250607_185025_10.json
2025-06-07 18:50:45,424 - INFO - 🔒 MODEL 10: Win Rate=37.31%, Composite Score=0.5083, Net Profit=$-0.90
2025-06-07 18:50:45,425 - INFO - 📝 STEP 4: Generating comprehensive report...
2025-06-07 18:50:45,429 - INFO - 🔒 COMPREHENSIVE REPORT: Generated html_reports/enhanced_training_report_20250607_185045.html
2025-06-07 18:50:45,429 - INFO - 🎉 ENHANCED TRAINING PIPELINE COMPLETED!
2025-06-07 18:50:45,429 - INFO - ==========================================================================================
2025-06-07 18:50:45,429 - INFO - 🏆 BEST COMPOSITE SCORE MODEL: enhanced_tcn_cnn_ppo_20250607_184858_5
2025-06-07 18:50:45,430 - INFO - 📊 Composite Score: 0.5087 (Target: ≥0.79)
2025-06-07 18:50:45,431 - INFO - 🎯 Win Rate: 37.31% (Target: ≥90%)
2025-06-07 18:50:45,434 - INFO - 💰 Net Profit: $-0.83
2025-06-07 18:50:45,435 - INFO - 
2025-06-07 18:50:45,435 - INFO - 💰 BEST NET PROFIT MODEL: enhanced_tcn_cnn_ppo_20250607_184858_5
2025-06-07 18:50:45,436 - INFO - 💵 Net Profit: $-0.83
2025-06-07 18:50:45,450 - INFO - 📊 Composite Score: 0.5087
2025-06-07 18:50:45,451 - INFO - 🎯 Win Rate: 37.31%
2025-06-07 18:50:45,465 - INFO - 
2025-06-07 18:50:45,479 - INFO - 💾 Best Models Saved:
2025-06-07 18:50:45,484 - INFO -    🏆 best_models/best_composite_score_model.json
2025-06-07 18:50:45,487 - INFO -    💰 best_models/best_net_profit_model.json
2025-06-07 18:50:45,488 - INFO - 📝 Report: html_reports/enhanced_training_report_20250607_185045.html
2025-06-07 18:50:45,488 - INFO - ⚠️ DEPLOYMENT STATUS: Needs more optimization
