2025-06-07 19:00:23,752 - INFO - 🔒 REAL DATA: Connected to Binance API
2025-06-07 19:00:23,752 - INFO - 🔒 UPDATED GRID SPECIFICATIONS:
2025-06-07 19:00:23,752 - INFO -    📏 Grid Spacing: 0.250% (0.25%)
2025-06-07 19:00:23,752 - INFO -    📈 Take Profit: 0.250% (0.25% - 1 grid level)
2025-06-07 19:00:23,752 - INFO -    📉 Stop Loss: 0.125% (0.125% - 0.5 grid level)
2025-06-07 19:00:23,752 - INFO -    ⚖️ Risk/Reward Ratio: 2:1 (Risk 0.125% to gain 0.25%)
2025-06-07 19:00:23,752 - INFO -    🎯 Max Positions: 8
2025-06-07 19:00:23,752 - INFO -    🎲 Confidence Threshold: 25.0%
2025-06-07 19:00:23,768 - INFO - 🚀 STARTING UPDATED TRAINING PIPELINE - 0.25% GRID SPACING
2025-06-07 19:00:23,768 - INFO - ================================================================================
2025-06-07 19:00:23,768 - INFO - 
🔒 UPDATED GRID TRADING SYSTEM SUMMARY
=====================================

📏 UPDATED GRID SPECIFICATIONS:
   • Grid Spacing: 0.25% (UPDATED from 0.125%)
   • Take Profit: 0.25% (1 grid level above/below)
   • Stop Loss: 0.125% (0.5 grid level opposite direction)
   • Risk/Reward Ratio: 2:1 (Risk 0.125% to gain 0.25%)
   • Max Positions: 8 (adjusted for wider spacing)
   • Confidence Threshold: 25.0% (higher for selectivity)

🔒 LOCKED TRADING CONDITIONS:
   • BUY: Enter at grid level, exit 0.25% above, stop 0.125% below
   • SELL: Enter at grid level, exit 0.25% below, stop 0.125% above
   • HOLD: Do nothing when confidence < 25.0%

🎯 PERFORMANCE TARGETS:
   • Win Rate: ≥90%
   • Composite Score: ≥0.79
   • Net Profit: Positive returns
   • Max Drawdown: <5%

📊 TRAINING SPECIFICATIONS:
   • Training Window: 60 days (LOCKED)
   • Testing Window: 30 days (LOCKED)
   • Total Data: 90 days real Binance data
   • Indicators: Exactly 4 (VWAP, BB, RSI, ETH/BTC)
   • Models: 10 variations with different weight combinations

🚀 EXPECTED IMPROVEMENTS WITH 0.25% SPACING:
   • Higher quality trades (more selective entry)
   • Better risk/reward per trade (2:1 ratio maintained)
   • Reduced trade frequency but higher profit potential
   • Lower transaction costs due to fewer trades
   • More stable performance in volatile markets

⚠️ TRADE-OFFS WITH WIDER SPACING:
   • Fewer total trades (lower frequency)
   • Requires stronger signals for entry
   • May miss some smaller profitable moves
   • Higher confidence threshold needed

🔒 SYSTEM READY FOR TRAINING AND TESTING WITH UPDATED SPECIFICATIONS
        
2025-06-07 19:00:23,772 - INFO - 📊 STEP 1: Collecting 90 days of real Binance data...
2025-06-07 19:00:23,772 - INFO - 🔒 REAL DATA: Collecting 90 days of 1-minute BTC/USDT data...
2025-06-07 19:00:33,471 - INFO - 🔒 REAL DATA: Collected 25000 candles...
2025-06-07 19:00:41,760 - INFO - 🔒 REAL DATA: Collected 50000 candles...
2025-06-07 19:00:49,772 - INFO - 🔒 REAL DATA: Collected 75000 candles...
2025-06-07 19:00:57,633 - INFO - 🔒 REAL DATA: Collected 100000 candles...
2025-06-07 19:01:05,986 - INFO - 🔒 REAL DATA: Collected 125000 candles...
2025-06-07 19:01:07,904 - INFO - 🔒 INDICATORS: Calculating exactly 4 locked indicators...
2025-06-07 19:01:08,411 - INFO - 🔒 ETH/BTC RATIO: 0.023833
2025-06-07 19:01:08,438 - INFO - 🔒 INDICATORS: All 4 locked indicators calculated
2025-06-07 19:01:08,453 - INFO - 🔒 REAL DATA: Collected 129522 real data points
2025-06-07 19:01:08,469 - INFO - ✂️ STEP 2: Splitting data - 60 days training, 30 days testing...
2025-06-07 19:01:08,500 - INFO - 🔒 DATA SPLIT: Training=86348 (60 days), Testing=43174 (30 days)
2025-06-07 19:01:08,500 - INFO - 🧠 STEP 3: Training 10 updated models with 0.25% grid spacing...
2025-06-07 19:01:08,500 - INFO - 🔒 UPDATED TRAINING: Creating and testing 10 models with 0.25% grid spacing...
2025-06-07 19:01:08,500 - INFO - 🔒 MODEL 1/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:08,500 - INFO - 🔒 ML TRAINING: Creating updated model 1 with 0.25% grid spacing...
2025-06-07 19:01:08,500 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:08,500 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:08,500 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:08,500 - INFO - 🔒 ML TRAINING: Model 1 training completed with 0.25% grid spacing
2025-06-07 19:01:08,500 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190108_1 with 0.25% grid spacing...
2025-06-07 19:01:13,478 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:14,578 - INFO - 🏆 NEW BEST COMPOSITE SCORE (0.25%): 0.5165
2025-06-07 19:01:15,080 - INFO - 💰 NEW BEST NET PROFIT (0.25%): $-0.87
2025-06-07 19:01:15,096 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190108_1.json
2025-06-07 19:01:15,096 - INFO - 🔒 MODEL 1: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:15,096 - INFO - 🔒 MODEL 2/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:15,096 - INFO - 🔒 ML TRAINING: Creating updated model 2 with 0.25% grid spacing...
2025-06-07 19:01:15,096 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:15,096 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:15,096 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:15,096 - INFO - 🔒 ML TRAINING: Model 2 training completed with 0.25% grid spacing
2025-06-07 19:01:15,111 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190115_2 with 0.25% grid spacing...
2025-06-07 19:01:19,643 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:20,137 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190115_2.json
2025-06-07 19:01:20,137 - INFO - 🔒 MODEL 2: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:20,137 - INFO - 🔒 MODEL 3/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:20,137 - INFO - 🔒 ML TRAINING: Creating updated model 3 with 0.25% grid spacing...
2025-06-07 19:01:20,137 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:20,137 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:20,137 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:20,137 - INFO - 🔒 ML TRAINING: Model 3 training completed with 0.25% grid spacing
2025-06-07 19:01:20,137 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190120_3 with 0.25% grid spacing...
2025-06-07 19:01:24,978 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:25,528 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190120_3.json
2025-06-07 19:01:25,528 - INFO - 🔒 MODEL 3: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:25,528 - INFO - 🔒 MODEL 4/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:25,528 - INFO - 🔒 ML TRAINING: Creating updated model 4 with 0.25% grid spacing...
2025-06-07 19:01:25,528 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:25,528 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:25,528 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:25,528 - INFO - 🔒 ML TRAINING: Model 4 training completed with 0.25% grid spacing
2025-06-07 19:01:25,528 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190125_4 with 0.25% grid spacing...
2025-06-07 19:01:30,113 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:30,633 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190125_4.json
2025-06-07 19:01:30,633 - INFO - 🔒 MODEL 4: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:30,633 - INFO - 🔒 MODEL 5/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:30,633 - INFO - 🔒 ML TRAINING: Creating updated model 5 with 0.25% grid spacing...
2025-06-07 19:01:30,633 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:30,649 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:30,654 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:30,654 - INFO - 🔒 ML TRAINING: Model 5 training completed with 0.25% grid spacing
2025-06-07 19:01:30,654 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190130_5 with 0.25% grid spacing...
2025-06-07 19:01:35,484 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5139 composite score, $-0.88 net profit
2025-06-07 19:01:36,002 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190130_5.json
2025-06-07 19:01:36,002 - INFO - 🔒 MODEL 5: Win Rate=35.32%, Composite Score=0.5139, Net Profit=$-0.88
2025-06-07 19:01:36,002 - INFO - 🔒 MODEL 6/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:36,002 - INFO - 🔒 ML TRAINING: Creating updated model 6 with 0.25% grid spacing...
2025-06-07 19:01:36,002 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:36,002 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:36,002 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:36,002 - INFO - 🔒 ML TRAINING: Model 6 training completed with 0.25% grid spacing
2025-06-07 19:01:36,002 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190136_6 with 0.25% grid spacing...
2025-06-07 19:01:40,586 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:41,058 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190136_6.json
2025-06-07 19:01:41,058 - INFO - 🔒 MODEL 6: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:41,058 - INFO - 🔒 MODEL 7/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:41,073 - INFO - 🔒 ML TRAINING: Creating updated model 7 with 0.25% grid spacing...
2025-06-07 19:01:41,073 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:41,073 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:41,073 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:41,073 - INFO - 🔒 ML TRAINING: Model 7 training completed with 0.25% grid spacing
2025-06-07 19:01:41,073 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190141_7 with 0.25% grid spacing...
2025-06-07 19:01:45,720 - INFO - 🔒 BACKTESTING: 5237 trades, 35.33% win rate, 0.5207 composite score, $-0.81 net profit
2025-06-07 19:01:46,727 - INFO - 🏆 NEW BEST COMPOSITE SCORE (0.25%): 0.5207
2025-06-07 19:01:47,260 - INFO - 💰 NEW BEST NET PROFIT (0.25%): $-0.81
2025-06-07 19:01:47,260 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190141_7.json
2025-06-07 19:01:47,260 - INFO - 🔒 MODEL 7: Win Rate=35.33%, Composite Score=0.5207, Net Profit=$-0.81
2025-06-07 19:01:47,260 - INFO - 🔒 MODEL 8/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:47,260 - INFO - 🔒 ML TRAINING: Creating updated model 8 with 0.25% grid spacing...
2025-06-07 19:01:47,260 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:47,260 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:47,260 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:47,260 - INFO - 🔒 ML TRAINING: Model 8 training completed with 0.25% grid spacing
2025-06-07 19:01:47,260 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190147_8 with 0.25% grid spacing...
2025-06-07 19:01:52,180 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:01:52,699 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190147_8.json
2025-06-07 19:01:52,699 - INFO - 🔒 MODEL 8: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:01:52,699 - INFO - 🔒 MODEL 9/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:52,699 - INFO - 🔒 ML TRAINING: Creating updated model 9 with 0.25% grid spacing...
2025-06-07 19:01:52,699 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:52,699 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:52,699 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:52,699 - INFO - 🔒 ML TRAINING: Model 9 training completed with 0.25% grid spacing
2025-06-07 19:01:52,699 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190152_9 with 0.25% grid spacing...
2025-06-07 19:01:57,395 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5171 composite score, $-0.86 net profit
2025-06-07 19:01:57,866 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190152_9.json
2025-06-07 19:01:57,866 - INFO - 🔒 MODEL 9: Win Rate=35.32%, Composite Score=0.5171, Net Profit=$-0.86
2025-06-07 19:01:57,866 - INFO - 🔒 MODEL 10/10: Training updated model with 0.25% spacing...
2025-06-07 19:01:57,866 - INFO - 🔒 ML TRAINING: Creating updated model 10 with 0.25% grid spacing...
2025-06-07 19:01:57,866 - INFO - 🔒 TCN: Training temporal convolutional network...
2025-06-07 19:01:57,866 - INFO - 🔒 CNN: Training convolutional neural network...
2025-06-07 19:01:57,866 - INFO - 🔒 PPO: Training proximal policy optimization...
2025-06-07 19:01:57,866 - INFO - 🔒 ML TRAINING: Model 10 training completed with 0.25% grid spacing
2025-06-07 19:01:57,882 - INFO - 🔒 BACKTESTING: Testing model updated_grid_tcn_cnn_ppo_20250607_190157_10 with 0.25% grid spacing...
2025-06-07 19:02:02,579 - INFO - 🔒 BACKTESTING: 5241 trades, 35.32% win rate, 0.5165 composite score, $-0.87 net profit
2025-06-07 19:02:03,101 - INFO - 🔒 MODEL SAVED: updated_models\model_updated_grid_tcn_cnn_ppo_20250607_190157_10.json
2025-06-07 19:02:03,101 - INFO - 🔒 MODEL 10: Win Rate=35.32%, Composite Score=0.5165, Net Profit=$-0.87
2025-06-07 19:02:03,101 - INFO - 📝 STEP 4: Generating results summary...
2025-06-07 19:02:03,101 - INFO - 🎉 UPDATED TRAINING PIPELINE COMPLETED!
2025-06-07 19:02:03,101 - INFO - ================================================================================
2025-06-07 19:02:03,101 - INFO - 🏆 BEST COMPOSITE SCORE MODEL (0.25%): updated_grid_tcn_cnn_ppo_20250607_190141_7
2025-06-07 19:02:03,101 - INFO - 📊 Composite Score: 0.5207 (Target: ≥0.79)
2025-06-07 19:02:03,101 - INFO - 🎯 Win Rate: 35.33% (Target: ≥90%)
2025-06-07 19:02:03,101 - INFO - 💰 Net Profit: $-0.81
2025-06-07 19:02:03,101 - INFO - 📈 Total Trades: 5237
2025-06-07 19:02:03,101 - INFO - 
2025-06-07 19:02:03,101 - INFO - 💰 BEST NET PROFIT MODEL (0.25%): updated_grid_tcn_cnn_ppo_20250607_190141_7
2025-06-07 19:02:03,101 - INFO - 💵 Net Profit: $-0.81
2025-06-07 19:02:03,101 - INFO - 📊 Composite Score: 0.5207
2025-06-07 19:02:03,101 - INFO - 🎯 Win Rate: 35.33%
2025-06-07 19:02:03,113 - INFO - 📈 Total Trades: 5237
2025-06-07 19:02:03,113 - INFO - 
2025-06-07 19:02:03,113 - INFO - 💾 Updated Models Saved:
2025-06-07 19:02:03,113 - INFO -    🏆 updated_models/best_composite_score_model_025.json
2025-06-07 19:02:03,113 - INFO -    💰 updated_models/best_net_profit_model_025.json
2025-06-07 19:02:03,113 - INFO - ⚠️ DEPLOYMENT STATUS: Needs more optimization
