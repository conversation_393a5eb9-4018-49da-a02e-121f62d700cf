#!/usr/bin/env python3
"""
LIVE TRADING SYSTEM - CONSERVATIVE ELITE
Independent deployment for live trading while ML development continues
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import time
import threading
from collections import defaultdict
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('live_trading.log'),
        logging.StreamHandler()
    ]
)

class LiveTradingSystem:
    """Independent Live Trading System - Conservative Elite"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt", initial_balance: float = 300.0):
        self.api_key_file = api_key_file
        self.exchange = None
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # Trading parameters (PROVEN PROFITABLE CONFIGURATION)
        self.grid_spacing = 0.0025  # 0.25%
        self.take_profit_pct = 0.0025  # 0.25% (grid-to-grid)
        self.stop_loss_pct = 0.00125   # 0.125% (half-grid)
        self.grid_proximity_threshold = 0.0001  # 0.01% (extremely close)
        
        # Risk management
        self.risk_amount_base = 10.0  # $10 base risk
        
        # Trading state
        self.open_trades = []
        self.completed_trades = []
        self.equity_curve = [initial_balance]
        self.is_running = False
        self.last_update = datetime.now()
        
        # Performance tracking
        self.daily_stats = defaultdict(dict)
        self.total_trades = 0
        self.winning_trades = 0
        
        self._connect_exchange()
    
    def _connect_exchange(self):
        """Connect to Binance for live trading"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,  # LIVE TRADING
                'enableRateLimit': True,
            })
            
            self.exchange.load_markets()
            logging.info("✅ Connected to Binance for LIVE TRADING")
            
            # Test connection
            balance = self.exchange.fetch_balance()
            usdt_balance = balance['USDT']['free']
            logging.info(f"💰 USDT Balance: ${usdt_balance:.2f}")
            
        except Exception as e:
            logging.error(f"❌ Failed to connect to Binance: {e}")
            raise
    
    def calculate_risk_amount(self, balance: float) -> float:
        """Calculate dynamic risk amount"""
        if balance <= 1000:
            return self.risk_amount_base  # $10
        
        excess = balance - 1000
        additional_increments = int(excess // 500)
        return self.risk_amount_base + (additional_increments * 10.0)
    
    def get_current_price(self) -> float:
        """Get current BTC/USDT price"""
        try:
            ticker = self.exchange.fetch_ticker('BTC/USDT')
            return float(ticker['last'])
        except Exception as e:
            logging.error(f"❌ Error fetching price: {e}")
            return None
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # VWAP (20-period)
        df['vwap'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / df['close']
        
        # RSI (5-period)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = df['rsi'] / 100
        
        # Bollinger Bands (20-period)
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0.5)
        
        return df
    
    def get_market_data(self, hours: int = 25) -> pd.DataFrame:
        """Get recent market data for indicators"""
        try:
            since = int((datetime.now() - timedelta(hours=hours)).timestamp() * 1000)
            ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1h', since=since, limit=hours)
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')
            
            return self.calculate_indicators(df)
            
        except Exception as e:
            logging.error(f"❌ Error fetching market data: {e}")
            return pd.DataFrame()
    
    def check_grid_proximity(self, price: float) -> Tuple[bool, float, float]:
        """Check if price is extremely close to grid level"""
        grid_size = price * self.grid_spacing
        grid_level = round(price / grid_size) * grid_size
        grid_proximity_ratio = abs(price - grid_level) / grid_level
        
        is_close = grid_proximity_ratio <= self.grid_proximity_threshold
        return is_close, grid_level, grid_proximity_ratio
    
    def generate_signal(self, current_price: float, df: pd.DataFrame) -> Tuple[Optional[str], float]:
        """Generate trading signal"""
        if df.empty or len(df) < 20:
            return None, 0.0
        
        # Check grid proximity first
        is_close, grid_level, proximity = self.check_grid_proximity(current_price)
        if not is_close:
            return None, 0.0
        
        # Get latest indicators
        latest = df.iloc[-1]
        
        # Check if indicators are available
        if pd.isna(latest['vwap_ratio']) or pd.isna(latest['rsi_norm']) or pd.isna(latest['bb_position']):
            return None, 0.0
        
        # Technical conditions
        vwap_ratio = latest['vwap_ratio']
        rsi = latest['rsi_norm']
        bb_position = latest['bb_position']
        
        # Signal conditions (PROVEN PROFITABLE)
        buy_signal = (rsi < 0.4 and bb_position < 0.4 and vwap_ratio > 0.998)
        sell_signal = (rsi > 0.6 and bb_position > 0.6 and vwap_ratio < 1.002)
        
        if buy_signal:
            confidence = 0.95 - (proximity * 1000)
            confidence = max(0.80, min(0.99, confidence))
            return "BUY", confidence
        elif sell_signal:
            confidence = 0.95 - (proximity * 1000)
            confidence = max(0.80, min(0.99, confidence))
            return "SELL", confidence
        
        return None, 0.0
    
    def place_order(self, direction: str, price: float, confidence: float) -> Optional[Dict]:
        """Place live order on Binance"""
        try:
            risk_amount = self.calculate_risk_amount(self.current_balance)
            
            # Calculate position size for exact risk amount
            quantity = risk_amount / (price * self.stop_loss_pct)
            
            # Calculate exit levels
            if direction == "BUY":
                take_profit_price = price * (1 + self.take_profit_pct)
                stop_loss_price = price * (1 - self.stop_loss_pct)
            else:  # SELL
                take_profit_price = price * (1 - self.take_profit_pct)
                stop_loss_price = price * (1 + self.stop_loss_pct)
            
            # Place market order (for immediate execution)
            side = 'buy' if direction == "BUY" else 'sell'
            
            # For demo purposes, we'll simulate the order
            # In production, uncomment the line below:
            # order = self.exchange.create_market_order('BTC/USDT', side, quantity)
            
            # Simulated order for safety
            order = {
                'id': f"sim_{int(time.time())}",
                'symbol': 'BTC/USDT',
                'side': side,
                'amount': quantity,
                'price': price,
                'status': 'filled',
                'timestamp': int(time.time() * 1000)
            }
            
            trade = {
                'id': order['id'],
                'direction': direction,
                'entry_price': price,
                'quantity': quantity,
                'take_profit_price': take_profit_price,
                'stop_loss_price': stop_loss_price,
                'entry_time': datetime.now(),
                'confidence': confidence,
                'risk_amount': risk_amount,
                'status': 'OPEN'
            }
            
            self.open_trades.append(trade)
            logging.info(f"🎯 {direction} Order Placed: ${price:,.2f} | Risk: ${risk_amount:.2f} | Confidence: {confidence:.1%}")
            
            return trade
            
        except Exception as e:
            logging.error(f"❌ Error placing order: {e}")
            return None
    
    def check_exits(self, current_price: float):
        """Check for trade exits"""
        for trade in self.open_trades[:]:
            direction = trade['direction']
            entry_price = trade['entry_price']
            
            profit_loss = 0
            exit_type = None
            
            if direction == "BUY":
                if current_price >= trade['take_profit_price']:
                    profit_loss = (trade['take_profit_price'] - entry_price) * trade['quantity']
                    exit_type = "TP"
                elif current_price <= trade['stop_loss_price']:
                    profit_loss = (trade['stop_loss_price'] - entry_price) * trade['quantity']
                    exit_type = "SL"
            
            else:  # SELL
                if current_price <= trade['take_profit_price']:
                    profit_loss = (entry_price - trade['take_profit_price']) * trade['quantity']
                    exit_type = "TP"
                elif current_price >= trade['stop_loss_price']:
                    profit_loss = (entry_price - trade['stop_loss_price']) * trade['quantity']
                    exit_type = "SL"
            
            if exit_type:
                # Close trade
                self.current_balance += profit_loss
                trade['exit_price'] = current_price
                trade['exit_time'] = datetime.now()
                trade['profit_loss'] = profit_loss
                trade['exit_type'] = exit_type
                trade['status'] = 'CLOSED'
                
                # Move to completed trades
                self.completed_trades.append(trade)
                self.open_trades.remove(trade)
                
                # Update statistics
                self.total_trades += 1
                if profit_loss > 0:
                    self.winning_trades += 1
                
                # Update equity curve
                self.equity_curve.append(self.current_balance)
                
                duration = (trade['exit_time'] - trade['entry_time']).total_seconds() / 3600
                logging.info(f"✅ {exit_type} {direction}: ${current_price:,.2f} | P&L: ${profit_loss:+.2f} | Duration: {duration:.1f}h")
    
    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        total_profit = self.current_balance - self.initial_balance
        total_return = (total_profit / self.initial_balance) * 100
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        return {
            'initial_balance': self.initial_balance,
            'current_balance': self.current_balance,
            'total_profit': total_profit,
            'total_return_pct': total_return,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.total_trades - self.winning_trades,
            'win_rate': win_rate,
            'open_trades': len(self.open_trades),
            'last_update': self.last_update.isoformat()
        }
    
    def trading_loop(self):
        """Main trading loop"""
        logging.info("🚀 STARTING LIVE TRADING LOOP")
        
        while self.is_running:
            try:
                # Get current price
                current_price = self.get_current_price()
                if not current_price:
                    time.sleep(30)
                    continue
                
                # Check exits for open trades
                self.check_exits(current_price)
                
                # Generate new signals only if no open trades (Conservative Elite)
                if len(self.open_trades) == 0:
                    # Get market data for indicators
                    df = self.get_market_data(25)
                    
                    if not df.empty:
                        direction, confidence = self.generate_signal(current_price, df)
                        
                        if direction and confidence > 0.8:
                            self.place_order(direction, current_price, confidence)
                
                # Update timestamp
                self.last_update = datetime.now()
                
                # Log status every 10 minutes
                if int(time.time()) % 600 == 0:
                    stats = self.get_performance_stats()
                    logging.info(f"📊 Balance: ${stats['current_balance']:.2f} | "
                               f"Profit: ${stats['total_profit']:+.2f} | "
                               f"Trades: {stats['total_trades']} | "
                               f"Win Rate: {stats['win_rate']:.1f}%")
                
                # Sleep for 1 minute
                time.sleep(60)
                
            except Exception as e:
                logging.error(f"❌ Error in trading loop: {e}")
                time.sleep(60)
    
    def start_trading(self):
        """Start the trading system"""
        if self.is_running:
            logging.warning("⚠️ Trading system already running")
            return
        
        self.is_running = True
        
        # Start trading in separate thread
        trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        trading_thread.start()
        
        logging.info("🚀 LIVE TRADING SYSTEM STARTED")
    
    def stop_trading(self):
        """Stop the trading system"""
        self.is_running = False
        logging.info("🛑 LIVE TRADING SYSTEM STOPPED")
    
    def save_state(self):
        """Save current state to file"""
        state = {
            'current_balance': self.current_balance,
            'completed_trades': self.completed_trades,
            'equity_curve': self.equity_curve,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'last_update': self.last_update.isoformat()
        }
        
        with open('live_trading_state.json', 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        logging.info("💾 Trading state saved")

def main():
    """Main function for standalone operation"""
    logging.info("🚀 INITIALIZING LIVE TRADING SYSTEM")
    
    # Create trading system
    trader = LiveTradingSystem(initial_balance=300.0)
    
    # Start trading
    trader.start_trading()
    
    try:
        # Keep running
        while True:
            time.sleep(300)  # Save state every 5 minutes
            trader.save_state()
    
    except KeyboardInterrupt:
        logging.info("🛑 Shutdown signal received")
        trader.stop_trading()
        trader.save_state()

if __name__ == "__main__":
    main()
