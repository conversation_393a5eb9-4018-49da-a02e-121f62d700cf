#!/usr/bin/env python3
"""
COMPREHENSIVE SYSTEM HEALTH CHECK
Diagnoses all components of the trading system
"""

import os
import sys
import json
import requests
import subprocess
import time
from datetime import datetime
import ccxt
import pandas as pd

class SystemHealthCheck:
    """Comprehensive health check for trading system"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'UNKNOWN',
            'components': {},
            'issues': [],
            'recommendations': []
        }
    
    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        print("🔍 CHECKING DEPENDENCIES...")
        
        required_packages = {
            'ccxt': 'Trading exchange connectivity',
            'pandas': 'Data processing',
            'numpy': 'Numerical computations',
            'flask': 'Web application framework',
            'requests': 'HTTP requests'
        }
        
        missing = []
        for package, description in required_packages.items():
            try:
                __import__(package)
                print(f"  ✅ {package} - {description}")
            except ImportError:
                print(f"  ❌ {package} - MISSING ({description})")
                missing.append(package)
        
        self.results['components']['dependencies'] = {
            'status': 'PASS' if not missing else 'FAIL',
            'missing_packages': missing
        }
        
        if missing:
            self.results['issues'].append(f"Missing packages: {', '.join(missing)}")
            self.results['recommendations'].append(f"Install with: pip install {' '.join(missing)}")
    
    def check_api_keys(self):
        """Check API key configuration"""
        print("\n🔑 CHECKING API KEYS...")
        
        api_file = 'BinanceAPI_2.txt'
        if os.path.exists(api_file):
            try:
                with open(api_file, 'r') as f:
                    lines = f.read().strip().split('\n')
                    if len(lines) >= 2 and lines[0].strip() and lines[1].strip():
                        print("  ✅ API key file found and properly formatted")
                        
                        # Test connection
                        try:
                            exchange = ccxt.binance({
                                'apiKey': lines[0].strip(),
                                'secret': lines[1].strip(),
                                'sandbox': False,
                                'enableRateLimit': True,
                            })
                            exchange.load_markets()
                            balance = exchange.fetch_balance()
                            usdt_balance = balance['USDT']['free']
                            
                            print(f"  ✅ Binance connection successful")
                            print(f"  💰 USDT Balance: ${usdt_balance:.2f}")
                            
                            self.results['components']['api_keys'] = {
                                'status': 'PASS',
                                'usdt_balance': usdt_balance,
                                'connection': 'SUCCESS'
                            }
                            
                        except Exception as e:
                            print(f"  ❌ Binance connection failed: {e}")
                            self.results['components']['api_keys'] = {
                                'status': 'FAIL',
                                'connection': 'FAILED',
                                'error': str(e)
                            }
                            self.results['issues'].append("Binance API connection failed")
                    else:
                        print("  ❌ API key file malformed")
                        self.results['components']['api_keys'] = {
                            'status': 'FAIL',
                            'error': 'Malformed API key file'
                        }
                        self.results['issues'].append("API key file malformed")
            except Exception as e:
                print(f"  ❌ Error reading API key file: {e}")
                self.results['components']['api_keys'] = {
                    'status': 'FAIL',
                    'error': str(e)
                }
                self.results['issues'].append("Cannot read API key file")
        else:
            print("  ❌ API key file not found")
            self.results['components']['api_keys'] = {
                'status': 'FAIL',
                'error': 'API key file not found'
            }
            self.results['issues'].append("API key file missing")
            self.results['recommendations'].append("Create BinanceAPI_2.txt with API key and secret")
    
    def check_web_app(self):
        """Check web application status"""
        print("\n🌐 CHECKING WEB APPLICATION...")
        
        try:
            # Test main dashboard
            response = requests.get('http://localhost:5000', timeout=5)
            if response.status_code == 200:
                print("  ✅ Dashboard accessible")
                dashboard_status = 'PASS'
            else:
                print(f"  ❌ Dashboard returned status {response.status_code}")
                dashboard_status = 'FAIL'
        except requests.exceptions.ConnectionError:
            print("  ❌ Dashboard not accessible (connection refused)")
            dashboard_status = 'FAIL'
        except Exception as e:
            print(f"  ❌ Dashboard error: {e}")
            dashboard_status = 'FAIL'
        
        # Test API endpoints
        api_endpoints = ['/api/status', '/api/trades', '/api/equity_curve']
        api_results = {}
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
                if response.status_code == 200:
                    print(f"  ✅ API {endpoint} working")
                    api_results[endpoint] = 'PASS'
                else:
                    print(f"  ❌ API {endpoint} returned {response.status_code}")
                    api_results[endpoint] = 'FAIL'
            except Exception as e:
                print(f"  ❌ API {endpoint} error: {e}")
                api_results[endpoint] = 'FAIL'
        
        overall_api_status = 'PASS' if all(status == 'PASS' for status in api_results.values()) else 'FAIL'
        
        self.results['components']['web_app'] = {
            'status': 'PASS' if dashboard_status == 'PASS' and overall_api_status == 'PASS' else 'FAIL',
            'dashboard': dashboard_status,
            'api_endpoints': api_results
        }
        
        if dashboard_status == 'FAIL':
            self.results['issues'].append("Web dashboard not accessible")
            self.results['recommendations'].append("Start web app with: python trading_webapp.py")
        
        if overall_api_status == 'FAIL':
            self.results['issues'].append("Some API endpoints not working")
    
    def check_trading_system(self):
        """Check trading system status"""
        print("\n🤖 CHECKING TRADING SYSTEM...")
        
        try:
            response = requests.get('http://localhost:5000/api/status', timeout=5)
            if response.status_code == 200:
                data = response.json()
                
                is_running = data.get('is_running', False)
                current_balance = data.get('current_balance', 0)
                total_trades = data.get('total_trades', 0)
                win_rate = data.get('win_rate', 0)
                open_trades = data.get('open_trades', 0)
                
                print(f"  📊 System Running: {'YES' if is_running else 'NO'}")
                print(f"  💰 Current Balance: ${current_balance:.2f}")
                print(f"  📈 Total Trades: {total_trades}")
                print(f"  🎯 Win Rate: {win_rate:.1f}%")
                print(f"  🔄 Open Trades: {open_trades}")
                
                self.results['components']['trading_system'] = {
                    'status': 'PASS' if is_running else 'WARN',
                    'is_running': is_running,
                    'current_balance': current_balance,
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'open_trades': open_trades
                }
                
                if not is_running:
                    self.results['recommendations'].append("Start trading system via web dashboard")
                
            else:
                print("  ❌ Cannot get trading system status")
                self.results['components']['trading_system'] = {
                    'status': 'FAIL',
                    'error': 'Cannot get status'
                }
                self.results['issues'].append("Trading system status unavailable")
        
        except Exception as e:
            print(f"  ❌ Trading system check failed: {e}")
            self.results['components']['trading_system'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            self.results['issues'].append("Trading system check failed")
    
    def check_files(self):
        """Check required files"""
        print("\n📁 CHECKING FILES...")
        
        required_files = {
            'live_trading_system.py': 'Main trading system',
            'trading_webapp.py': 'Web application',
            'BinanceAPI_2.txt': 'API credentials'
        }
        
        optional_files = {
            'live_trading.log': 'Trading logs',
            'live_trading_state.json': 'System state',
            'templates/dashboard.html': 'Dashboard template'
        }
        
        file_status = {}
        
        for file, description in required_files.items():
            if os.path.exists(file):
                print(f"  ✅ {file} - {description}")
                file_status[file] = 'PRESENT'
            else:
                print(f"  ❌ {file} - MISSING ({description})")
                file_status[file] = 'MISSING'
                self.results['issues'].append(f"Required file missing: {file}")
        
        for file, description in optional_files.items():
            if os.path.exists(file):
                print(f"  ✅ {file} - {description}")
                file_status[file] = 'PRESENT'
            else:
                print(f"  ⚠️  {file} - Not found ({description})")
                file_status[file] = 'MISSING'
        
        self.results['components']['files'] = {
            'status': 'PASS' if all(status == 'PRESENT' for file, status in file_status.items() if file in required_files) else 'FAIL',
            'file_status': file_status
        }
    
    def check_logs(self):
        """Check log files for errors"""
        print("\n📝 CHECKING LOGS...")
        
        log_file = 'live_trading.log'
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                recent_lines = lines[-50:] if len(lines) > 50 else lines
                error_count = sum(1 for line in recent_lines if 'ERROR' in line or 'CRITICAL' in line)
                warning_count = sum(1 for line in recent_lines if 'WARNING' in line)
                
                print(f"  📊 Total log lines: {len(lines)}")
                print(f"  ❌ Recent errors: {error_count}")
                print(f"  ⚠️  Recent warnings: {warning_count}")
                
                if error_count > 0:
                    print("  🔍 Recent errors found:")
                    for line in recent_lines:
                        if 'ERROR' in line or 'CRITICAL' in line:
                            print(f"    {line.strip()}")
                
                self.results['components']['logs'] = {
                    'status': 'PASS' if error_count == 0 else 'WARN',
                    'total_lines': len(lines),
                    'recent_errors': error_count,
                    'recent_warnings': warning_count
                }
                
                if error_count > 0:
                    self.results['issues'].append(f"Found {error_count} recent errors in logs")
                
            except Exception as e:
                print(f"  ❌ Error reading log file: {e}")
                self.results['components']['logs'] = {
                    'status': 'FAIL',
                    'error': str(e)
                }
        else:
            print("  ⚠️  No log file found")
            self.results['components']['logs'] = {
                'status': 'WARN',
                'error': 'No log file found'
            }
    
    def determine_overall_status(self):
        """Determine overall system health"""
        component_statuses = [comp.get('status', 'FAIL') for comp in self.results['components'].values()]
        
        if all(status == 'PASS' for status in component_statuses):
            self.results['overall_status'] = 'HEALTHY'
        elif any(status == 'FAIL' for status in component_statuses):
            self.results['overall_status'] = 'CRITICAL'
        else:
            self.results['overall_status'] = 'WARNING'
    
    def print_summary(self):
        """Print health check summary"""
        print("\n" + "="*60)
        print("📊 SYSTEM HEALTH CHECK SUMMARY")
        print("="*60)
        
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '❌'
        }
        
        print(f"Overall Status: {status_emoji.get(self.results['overall_status'], '❓')} {self.results['overall_status']}")
        print(f"Check Time: {self.results['timestamp']}")
        print()
        
        print("Component Status:")
        for component, data in self.results['components'].items():
            status = data.get('status', 'UNKNOWN')
            emoji = '✅' if status == 'PASS' else '⚠️' if status == 'WARN' else '❌'
            print(f"  {emoji} {component.upper()}: {status}")
        
        if self.results['issues']:
            print(f"\n❌ Issues Found ({len(self.results['issues'])}):")
            for i, issue in enumerate(self.results['issues'], 1):
                print(f"  {i}. {issue}")
        
        if self.results['recommendations']:
            print(f"\n💡 Recommendations ({len(self.results['recommendations'])}):")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*60)
    
    def save_report(self):
        """Save health check report"""
        filename = f"health_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"📄 Health check report saved: {filename}")
    
    def run_full_check(self):
        """Run complete health check"""
        print("🏥 STARTING COMPREHENSIVE SYSTEM HEALTH CHECK")
        print("="*60)
        
        self.check_dependencies()
        self.check_api_keys()
        self.check_files()
        self.check_web_app()
        self.check_trading_system()
        self.check_logs()
        
        self.determine_overall_status()
        self.print_summary()
        self.save_report()
        
        return self.results

def main():
    """Run health check"""
    health_check = SystemHealthCheck()
    results = health_check.run_full_check()
    
    # Exit with appropriate code
    if results['overall_status'] == 'HEALTHY':
        sys.exit(0)
    elif results['overall_status'] == 'WARNING':
        sys.exit(1)
    else:  # CRITICAL
        sys.exit(2)

if __name__ == "__main__":
    main()
