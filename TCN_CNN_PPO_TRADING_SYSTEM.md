# TCN-CNN-PPO Trading System Specification

## 🎯 **SYSTEM OVERVIEW**

### **Model Architecture**
- **TCN (Temporal Convolutional Network)**: Time-series pattern recognition
- **CNN (Convolutional Neural Network)**: Spatial feature extraction
- **PPO (Proximal Policy Optimization)**: Reinforcement learning for decision making

### **Trading Strategy**
- **Type**: Grid-based trading with ML signal confirmation
- **Market**: BTC/USDT (primary)
- **Timeframe**: 1-hour candles
- **Approach**: Conservative Elite with high-probability entries

---

## 📊 **GRID TRADING SPECIFICATIONS**

### **Grid Configuration**
- **Grid Spacing**: 0.25% (0.0025)
- **Grid Calculation**: 
  ```python
  grid_size = current_price * 0.0025
  grid_level = round(current_price / grid_size) * grid_size
  ```

### **Entry Requirements**
- **Grid Proximity**: Price must hit grid level OR be extremely close
- **Proximity Threshold**: Within 0.01% of grid level (99.9% accuracy)
- **Entry Condition**: `abs(current_price - grid_level) / grid_level <= 0.0001`

### **Exit Specifications**
- **Take Profit**: Next grid level (0.25% from entry)
- **Stop Loss**: 0.125% from entry (half grid spacing)
- **Risk/Reward Ratio**: 1:2 (0.125% risk, 0.25% reward)

---

## 💰 **RISK MANAGEMENT**

### **Dynamic Risk Scaling**
```python
def calculate_risk_amount(account_balance):
    if account_balance <= 1000:
        return 10.0
    excess = account_balance - 1000
    additional_increments = int(excess // 500)
    return 10.0 + (additional_increments * 10.0)
```

### **Examples**
- $300 account → $10 risk per trade
- $1000 account → $10 risk per trade
- $1500 account → $20 risk per trade
- $2000 account → $30 risk per trade

### **Position Sizing**
```python
stop_loss_percentage = 0.00125  # 0.125%
position_size = risk_amount / (entry_price * stop_loss_percentage)
```

---

## 📈 **TECHNICAL INDICATORS**

### **Primary Indicators (Core 4)**
1. **VWAP (20-period)**
   - Calculation: `(price × volume).rolling(20).sum() / volume.rolling(20).sum()`
   - Usage: Trend direction and support/resistance

2. **RSI (5-period)**
   - Fast RSI for quick momentum detection
   - Normalized to 0-1 range

3. **Bollinger Bands Position (20-period, 2σ)**
   - Position within bands: `(price - lower) / (upper - lower)`
   - Volatility and mean reversion signals

4. **ETH/BTC Ratio**
   - Market sentiment indicator
   - Crypto correlation analysis

### **Secondary Features (8 Additional)**
5. Price change percentage
6. Volume change percentage  
7. High/Low ratio
8. Close/Open ratio
9. Grid distance (normalized)
10. Raw OHLC values
11. Grid level calculation
12. Grid proximity score

---

## 🎯 **SIGNAL GENERATION**

### **Entry Conditions**
```python
# Grid proximity check (MANDATORY)
grid_proximity = abs(current_price - grid_level) / grid_level
if grid_proximity > 0.0001:  # Must be extremely close to grid
    return None, 0.0

# Technical confirmation
buy_signal = (rsi < 0.4 and bb_position < 0.4 and vwap_ratio > 0.998)
sell_signal = (rsi > 0.6 and bb_position > 0.6 and vwap_ratio < 1.002)

# Confidence calculation
confidence = 0.95 - (grid_proximity * 1000)  # Higher confidence closer to grid
```

### **Trade Execution Rules**
- **NO time limits** between trades
- **NO maximum** daily trade count
- **Grid proximity**: MANDATORY requirement
- **Confidence threshold**: >80%

---

## 📊 **COMPOSITE SCORE METRICS**

### **Formula**
```python
composite_score = (
    0.25 * sortino_normalized +      # 25% - Risk-adjusted returns
    0.20 * ulcer_index_inverse +     # 20% - Drawdown quality  
    0.15 * equity_curve_r2 +         # 15% - Growth consistency
    0.15 * profit_stability +        # 15% - Return stability
    0.15 * upward_move_ratio +       # 15% - Positive momentum
    0.10 * drawdown_duration_inverse # 10% - Recovery speed
) * 100
```

### **Metric Calculations**

#### **1. Sortino Ratio (25%)**
```python
def calculate_sortino_ratio(returns):
    downside_returns = returns[returns < 0]
    downside_deviation = np.std(downside_returns)
    return np.mean(returns) / downside_deviation
```

#### **2. Ulcer Index Inverse (20%)**
```python
def calculate_ulcer_index(equity_curve):
    running_max = np.maximum.accumulate(equity_curve)
    drawdowns = (equity_curve - running_max) / running_max * 100
    return np.sqrt(np.mean(drawdowns ** 2))
```

#### **3. Equity Curve R² (15%)**
```python
def calculate_equity_r2(equity_curve):
    x = np.arange(len(equity_curve))
    return r2_score(equity_curve, np.polyval(np.polyfit(x, equity_curve, 1), x))
```

#### **4. Profit Stability (15%)**
```python
def calculate_profit_stability(returns):
    return 1.0 / (1.0 + np.std(returns))
```

#### **5. Upward Move Ratio (15%)**
```python
def calculate_upward_ratio(equity_curve):
    changes = np.diff(equity_curve)
    return np.sum(changes > 0) / len(changes)
```

#### **6. Drawdown Duration Inverse (10%)**
```python
def calculate_drawdown_duration_inverse(equity_curve):
    # Calculate average time to recover from drawdowns
    running_max = np.maximum.accumulate(equity_curve)
    in_drawdown = equity_curve < running_max
    # ... duration calculation logic
    return 1.0 / (1.0 + average_duration)
```

---

## 🚀 **TRAINING SPECIFICATIONS**

### **Data Requirements**
- **Total Period**: 90 days of 1-hour candles
- **Training Data**: 60 days (first 2/3)
- **Testing Data**: 30 days (last 1/3)
- **Sequence Length**: 24 hours (24 candles)

### **Training Targets**
- **Win Rate**: ≥90% on out-of-sample testing data
- **Composite Score**: >79 points
- **Training Method**: Train until targets achieved

### **Model Selection Criteria**
1. **Primary**: Highest composite score
2. **Secondary**: Highest net profit
3. **Validation**: Out-of-sample performance only

---

## 🧪 **TESTING PROTOCOL**

### **Backtest Parameters**
- **Starting Balance**: $300
- **Test Period**: 30 days (most recent)
- **Risk Scaling**: Dynamic based on balance
- **Max Open Trades**: 1 (Conservative Elite)

### **Performance Validation**
```python
# Entry validation
def validate_entry(current_price, grid_level):
    proximity = abs(current_price - grid_level) / grid_level
    return proximity <= 0.0001  # Extremely close to grid

# Exit validation  
def validate_exits(entry_price, current_price, direction):
    if direction == "BUY":
        take_profit = entry_price * 1.0025    # +0.25%
        stop_loss = entry_price * 0.99875     # -0.125%
    else:  # SELL
        take_profit = entry_price * 0.9975    # -0.25%
        stop_loss = entry_price * 1.00125     # +0.125%
    
    return take_profit, stop_loss
```

### **Success Criteria**
- **Win Rate**: ≥90% (mandatory)
- **Composite Score**: >79 (mandatory)
- **Risk Control**: Perfect $10 risk per trade
- **Grid Adherence**: 100% grid-level entries

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **Model Architecture**
- [ ] TCN blocks with proper dilations
- [ ] CNN feature extraction layers
- [ ] PPO actor-critic heads
- [ ] Confidence scoring output

### **Grid System**
- [ ] 0.25% grid spacing calculation
- [ ] Extremely close proximity requirement (0.01%)
- [ ] Grid-to-grid profit taking (0.25%)
- [ ] Half-grid stop loss (0.125%)

### **Risk Management**
- [ ] Dynamic risk scaling implementation
- [ ] Position sizing based on 0.125% stop loss
- [ ] No time limits between trades
- [ ] No maximum daily trade limits

### **Performance Metrics**
- [ ] All 6 composite score components
- [ ] Proper metric weighting (25%, 20%, 15%, 15%, 15%, 10%)
- [ ] Out-of-sample validation
- [ ] Model selection criteria

### **Training Process**
- [ ] 90-day data collection
- [ ] 60/30 day train/test split
- [ ] Target achievement validation
- [ ] Best model saving (composite + profit)

---

## 🎯 **EXPECTED OUTCOMES**

### **Performance Targets**
- **Win Rate**: 90%+ (model requirement)
- **Composite Score**: >79 (approval threshold)  
- **Monthly Return**: 50-200% (based on 2:1 ratio)
- **Maximum Drawdown**: <15%

### **Risk Characteristics**
- **Risk per Trade**: $10 (scalable)
- **Recovery Speed**: Fast (measured in composite score)
- **Consistency**: High (profit stability component)
- **Grid Discipline**: 100% adherence to levels

**This system combines advanced machine learning with precise grid trading mechanics to achieve consistent, high-probability trades with excellent risk management.**
