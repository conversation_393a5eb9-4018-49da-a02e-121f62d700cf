#!/usr/bin/env python3
"""
Real-time 1-minute data collector for the updated trading system
Collects 90 days of 1-minute data for training and testing
"""

import ccxt
import sqlite3
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class RealtimeDataCollector:
    """Collects real-time 1-minute OHLCV data from Binance"""
    
    def __init__(self, api_key_file: str = "BinanceAPI_2.txt"):
        self.api_key_file = api_key_file
        self.exchange = None
        self.db_path = "realtime_1m_data.db"
        self.symbol = "BTC/USDT"
        self.timeframe = "1m"
        self.data_days = 90  # 90 days of data
        
        self._setup_database()
        self._connect_exchange()
    
    def _setup_database(self):
        """Setup SQLite database for 1-minute data"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS ohlcv_1m (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER UNIQUE,
                    datetime TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS indicators_1m (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER UNIQUE,
                    datetime TEXT,
                    vwap REAL,
                    rsi_5 REAL,
                    bb_position REAL,
                    eth_btc_ratio REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_ohlcv_timestamp ON ohlcv_1m(timestamp)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_indicators_timestamp ON indicators_1m(timestamp)
            ''')
    
    def _connect_exchange(self):
        """Connect to Binance exchange"""
        try:
            # Load API credentials
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',
                }
            })
            
            # Test connection
            self.exchange.load_markets()
            print("✅ Connected to Binance for data collection")
            
        except Exception as e:
            print(f"❌ Failed to connect to Binance: {e}")
            self.exchange = None
    
    def collect_historical_data(self, days: int = 90):
        """Collect historical 1-minute data for specified days"""
        if not self.exchange:
            print("❌ Exchange not connected")
            return False
        
        print(f"📊 Collecting {days} days of 1-minute historical data...")
        
        # Calculate start time (days ago)
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # Convert to milliseconds
        since = int(start_time.timestamp() * 1000)
        
        try:
            # Fetch historical data in chunks (Binance limit: 1000 candles per request)
            all_data = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                print(f"📈 Fetching data from {datetime.fromtimestamp(current_since/1000)}")
                
                ohlcv = self.exchange.fetch_ohlcv(
                    self.symbol, 
                    self.timeframe, 
                    since=current_since, 
                    limit=1000
                )
                
                if not ohlcv:
                    break
                
                all_data.extend(ohlcv)
                current_since = ohlcv[-1][0] + 60000  # Add 1 minute
                
                # Rate limiting
                time.sleep(0.1)
            
            # Store data in database
            self._store_ohlcv_data(all_data)
            print(f"✅ Collected {len(all_data)} 1-minute candles")
            return True
            
        except Exception as e:
            print(f"❌ Error collecting historical data: {e}")
            return False
    
    def _store_ohlcv_data(self, ohlcv_data: List):
        """Store OHLCV data in database"""
        with sqlite3.connect(self.db_path) as conn:
            for candle in ohlcv_data:
                timestamp, open_price, high, low, close, volume = candle
                dt = datetime.fromtimestamp(timestamp / 1000)
                
                conn.execute('''
                    INSERT OR REPLACE INTO ohlcv_1m 
                    (timestamp, datetime, open, high, low, close, volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (timestamp, dt.isoformat(), open_price, high, low, close, volume))
            
            conn.commit()
    
    def get_training_data(self) -> Dict:
        """Get 60 days of training data"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=60)
        
        return self._get_data_range(start_time, end_time)
    
    def get_testing_data(self) -> Dict:
        """Get 30 days of testing data (most recent)"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        
        return self._get_data_range(start_time, end_time)
    
    def _get_data_range(self, start_time: datetime, end_time: datetime) -> Dict:
        """Get data for specified time range"""
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('''
                SELECT timestamp, datetime, open, high, low, close, volume
                FROM ohlcv_1m
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_ts, end_ts))
            
            data = cursor.fetchall()
            
            return {
                'data': data,
                'count': len(data),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }
    
    def start_realtime_collection(self):
        """Start real-time data collection"""
        print("🚀 Starting real-time 1-minute data collection...")
        
        while True:
            try:
                # Get latest candle
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=1)
                
                if ohlcv:
                    self._store_ohlcv_data(ohlcv)
                    timestamp = ohlcv[0][0]
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    close_price = ohlcv[0][4]
                    print(f"📊 {dt}: BTC ${close_price:,.2f}")
                
                # Wait for next minute
                time.sleep(60)
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping real-time data collection")
                break
            except Exception as e:
                print(f"❌ Error in real-time collection: {e}")
                time.sleep(10)

def main():
    """Main function for data collection"""
    collector = RealtimeDataCollector()
    
    # Collect historical data first
    if collector.collect_historical_data(90):
        print("✅ Historical data collection complete")
        
        # Get training and testing data info
        training_data = collector.get_training_data()
        testing_data = collector.get_testing_data()
        
        print(f"📊 Training Data: {training_data['count']} candles")
        print(f"📊 Testing Data: {testing_data['count']} candles")
        
        # Start real-time collection
        collector.start_realtime_collection()
    else:
        print("❌ Failed to collect historical data")

if __name__ == "__main__":
    main()
