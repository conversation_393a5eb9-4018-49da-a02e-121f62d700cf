#!/usr/bin/env python3
"""
Dashboard Test and Validation
Tests all dashboard functionality
"""

import requests
import json
import time

def test_dashboard():
    """Test dashboard functionality"""
    base_url = "http://localhost:5000"
    
    print("🧪 TESTING DASHBOARD FUNCTIONALITY")
    print("="*50)
    
    # Test 1: Main dashboard
    print("1. Testing main dashboard...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ Dashboard loads successfully")
        else:
            print(f"   ❌ Dashboard returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard error: {e}")
    
    # Test 2: API Status
    print("\n2. Testing API status...")
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Status API working")
            print(f"   📊 Running: {data.get('is_running', False)}")
            print(f"   💰 Balance: ${data.get('current_balance', 0):.2f}")
            print(f"   📈 Trades: {data.get('total_trades', 0)}")
        else:
            print(f"   ❌ Status API returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status API error: {e}")
    
    # Test 3: Start trading system
    print("\n3. Testing trading system start...")
    try:
        response = requests.post(f"{base_url}/api/start", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ Trading system started successfully")
            else:
                print(f"   ⚠️  Start response: {data.get('message', 'Unknown')}")
        else:
            print(f"   ❌ Start API returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Start API error: {e}")
    
    # Test 4: Check status after start
    print("\n4. Checking status after start...")
    time.sleep(2)  # Wait for system to start
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Status check successful")
            print(f"   📊 Running: {data.get('is_running', False)}")
            print(f"   💰 Balance: ${data.get('current_balance', 0):.2f}")
            print(f"   📈 Trades: {data.get('total_trades', 0)}")
            print(f"   🎯 Win Rate: {data.get('win_rate', 0):.1f}%")
            print(f"   🔄 Open Trades: {data.get('open_trades', 0)}")
        else:
            print(f"   ❌ Status check returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status check error: {e}")
    
    # Test 5: Trades API
    print("\n5. Testing trades API...")
    try:
        response = requests.get(f"{base_url}/api/trades", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Trades API working ({len(data)} trades)")
        else:
            print(f"   ❌ Trades API returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Trades API error: {e}")
    
    # Test 6: Equity curve API
    print("\n6. Testing equity curve API...")
    try:
        response = requests.get(f"{base_url}/api/equity_curve", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Equity curve API working ({len(data)} data points)")
        else:
            print(f"   ❌ Equity curve API returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Equity curve API error: {e}")
    
    print("\n" + "="*50)
    print("🎉 DASHBOARD TEST COMPLETE")
    print("📊 Dashboard URL: http://localhost:5000")
    print("🔄 Refresh the page to see live updates")

if __name__ == "__main__":
    test_dashboard()
