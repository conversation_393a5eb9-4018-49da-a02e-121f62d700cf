<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Freedom Live Trading</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #ffd700;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            color: #90EE90;
            margin-bottom: 10px;
        }
        
        .model-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background-color: #00ff00; }
        .status-stopped { background-color: #ff4444; }
        .status-warning { background-color: #ffaa00; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #90EE90;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .btn-stop {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .trades-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .trades-table th,
        .trades-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .trades-table th {
            background: rgba(255,255,255,0.1);
            color: #ffd700;
        }
        
        .profit { color: #00ff00; }
        .loss { color: #ff4444; }
        
        .cog-icon {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255,215,0,0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            color: #000;
            transition: all 0.3s ease;
        }
        
        .cog-icon:hover {
            transform: rotate(90deg);
            background: rgba(255,215,0,1);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 15% auto;
            padding: 20px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            color: white;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: white;
        }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 10px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Bitcoin Freedom Live Trading</h1>
        <div class="subtitle">🔒 Conservative Elite System (93.2% Win Rate) - LOCKED</div>
        <div class="model-badge">🏆 Conservative Elite Model - 93.2% Performance</div>
    </div>
    
    <div class="auto-refresh">
        <span id="refresh-indicator">🔄 Auto-refresh: ON</span>
    </div>
    
    <div class="container">
        <div class="controls">
            <button class="btn" onclick="startTrading()">🚀 Start Trading</button>
            <button class="btn btn-stop" onclick="stopTrading()">🛑 Stop Trading</button>
        </div>
        
        <div class="dashboard-grid">
            <!-- Trading Status Card -->
            <div class="card">
                <h3>📊 Trading Status</h3>
                <div class="metric">
                    <span>System Status:</span>
                    <span class="metric-value">
                        <span class="status-indicator" id="status-indicator"></span>
                        <span id="system-status">Loading...</span>
                    </span>
                </div>
                <div class="metric">
                    <span>Model:</span>
                    <span class="metric-value" id="model-name">Conservative Elite</span>
                </div>
                <div class="metric">
                    <span>Win Rate:</span>
                    <span class="metric-value" id="win-rate">93.2%</span>
                </div>
                <div class="metric">
                    <span>Composite Score:</span>
                    <span class="metric-value" id="composite-score">79.1%</span>
                </div>
                <div class="metric">
                    <span>Trades Today:</span>
                    <span class="metric-value" id="trades-today">0</span>
                </div>
            </div>
            
            <!-- Market Data Card -->
            <div class="card">
                <h3>💰 Market Data</h3>
                <div class="metric">
                    <span>BTC Price:</span>
                    <span class="metric-value" id="btc-price">$0.00</span>
                </div>
                <div class="metric">
                    <span>USDT Balance:</span>
                    <span class="metric-value" id="usdt-balance">$0.00</span>
                </div>
                <div class="metric">
                    <span>BTC Balance:</span>
                    <span class="metric-value" id="btc-balance">0.00000000</span>
                </div>
                <div class="metric">
                    <span>Open Trades:</span>
                    <span class="metric-value" id="open-trades">0</span>
                </div>
            </div>
            
            <!-- Recent Trades Card -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>📈 Recent Trades</h3>
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Direction</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>P&L</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="trades-tbody">
                        <tr>
                            <td colspan="6" style="text-align: center;">Loading trades...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Status Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>🔧 System Status</h3>
            <div id="modal-content">Loading...</div>
        </div>
    </div>
    
    <!-- Cog Icon -->
    <div class="cog-icon" onclick="showStatusModal()">⚙️</div>
    
    <script>
        let refreshInterval;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            startAutoRefresh();
        });
        
        function updateDashboard() {
            // Update trading status
            fetch('/api/trading_status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('system-status').textContent = data.is_running ? 'RUNNING' : 'STOPPED';
                    document.getElementById('status-indicator').className = 'status-indicator ' + 
                        (data.is_running ? 'status-running' : 'status-stopped');
                    
                    document.getElementById('model-name').textContent = data.model_name || 'Conservative Elite';
                    document.getElementById('win-rate').textContent = ((data.win_rate || 0.932) * 100).toFixed(1) + '%';
                    document.getElementById('composite-score').textContent = ((data.composite_score || 0.791) * 100).toFixed(1) + '%';
                    document.getElementById('trades-today').textContent = data.trades_today || 0;
                    
                    document.getElementById('btc-price').textContent = '$' + (data.current_price || 0).toLocaleString();
                    document.getElementById('open-trades').textContent = data.open_trades || 0;
                    
                    if (data.balance) {
                        document.getElementById('usdt-balance').textContent = '$' + (data.balance.USDT?.free || 0).toFixed(2);
                        document.getElementById('btc-balance').textContent = (data.balance.BTC?.free || 0).toFixed(8);
                    }
                    
                    // Update recent trades
                    updateRecentTrades(data.recent_trades || []);
                })
                .catch(error => console.error('Error updating dashboard:', error));
        }
        
        function updateRecentTrades(trades) {
            const tbody = document.getElementById('trades-tbody');
            
            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">No recent trades</td></tr>';
                return;
            }
            
            tbody.innerHTML = trades.map(trade => {
                const time = new Date(trade.timestamp || trade.created_at).toLocaleTimeString();
                const profitClass = (trade.profit_loss || 0) >= 0 ? 'profit' : 'loss';
                const profitText = trade.profit_loss ? '$' + trade.profit_loss.toFixed(2) : '-';
                
                return `
                    <tr>
                        <td>${time}</td>
                        <td>${trade.direction}</td>
                        <td>$${(trade.entry_price || 0).toLocaleString()}</td>
                        <td>${trade.exit_price ? '$' + trade.exit_price.toLocaleString() : '-'}</td>
                        <td class="${profitClass}">${profitText}</td>
                        <td>${trade.status}</td>
                    </tr>
                `;
            }).join('');
        }
        
        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('✅ ' + data.message);
                    updateDashboard();
                })
                .catch(error => {
                    console.error('Error starting trading:', error);
                    alert('❌ Error starting trading');
                });
        }
        
        function stopTrading() {
            if (confirm('Are you sure you want to stop trading?')) {
                fetch('/api/stop_trading', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert('🛑 ' + data.message);
                        updateDashboard();
                    })
                    .catch(error => {
                        console.error('Error stopping trading:', error);
                        alert('❌ Error stopping trading');
                    });
            }
        }
        
        function showStatusModal() {
            const modal = document.getElementById('statusModal');
            const content = document.getElementById('modal-content');
            
            content.innerHTML = 'Loading system status...';
            modal.style.display = 'block';
            
            fetch('/api/health_check')
                .then(response => response.json())
                .then(data => {
                    content.innerHTML = `
                        <div class="metric">
                            <span>Overall Status:</span>
                            <span class="metric-value">${data.overall_status}</span>
                        </div>
                        <div class="metric">
                            <span>Database:</span>
                            <span class="metric-value">${data.checks.database}</span>
                        </div>
                        <div class="metric">
                            <span>Binance:</span>
                            <span class="metric-value">${data.checks.binance}</span>
                        </div>
                        <div class="metric">
                            <span>Trading Engine:</span>
                            <span class="metric-value">${data.checks.trading_engine}</span>
                        </div>
                        ${data.checks.recent_win_rate ? `
                        <div class="metric">
                            <span>Recent Win Rate:</span>
                            <span class="metric-value">${data.checks.recent_win_rate}</span>
                        </div>
                        ` : ''}
                        ${data.issues.length > 0 ? `
                        <h4 style="color: #ffaa00; margin-top: 15px;">Issues:</h4>
                        <ul style="margin-left: 20px;">
                            ${data.issues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                        ` : ''}
                    `;
                })
                .catch(error => {
                    content.innerHTML = 'Error loading system status: ' + error.message;
                });
        }
        
        function startAutoRefresh() {
            refreshInterval = setInterval(updateDashboard, 5000); // Refresh every 5 seconds
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
        
        // Modal close functionality
        document.querySelector('.close').onclick = function() {
            document.getElementById('statusModal').style.display = 'none';
        }
        
        window.onclick = function(event) {
            const modal = document.getElementById('statusModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
