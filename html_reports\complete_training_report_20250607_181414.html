
        <!DOCTYPE html>
        <html>
        <head>
            <title>🔒 Complete Training Pipeline Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .header { background: #2c3e50; color: white; padding: 20px; border-radius: 10px; }
                .section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; }
                .metric { display: inline-block; margin: 10px; padding: 15px; background: #ecf0f1; border-radius: 5px; }
                .success { color: #27ae60; font-weight: bold; }
                .warning { color: #e74c3c; font-weight: bold; }
                .locked { color: #8e44ad; font-weight: bold; }
                .best { background: #f39c12; color: white; padding: 5px; border-radius: 3px; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background: #34495e; color: white; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔒 COMPLETE TRAINING PIPELINE REPORT</h1>
                <p>Generated: 2025-06-07 18:14:14 UTC</p>
                <p class="locked">🔒 TRAINED 5 MODELS - BEST MODEL SAVED</p>
            </div>

            <div class="section">
                <h2>🏆 BEST MODEL PERFORMANCE</h2>
                <div class="metric best">🏆 <strong>Best Model ID:</strong> tcn_cnn_ppo_20250607_181319</div>
                <div class="metric">📈 <strong>Total Trades:</strong> 15751</div>
                <div class="metric">🎯 <strong>Win Rate:</strong> 37.36%</div>
                <div class="metric">💰 <strong>Total P&L:</strong> $-0.93</div>
                <div class="metric">📊 <strong>Return:</strong> -0.31%</div>
                <div class="metric">🔒 <strong>Robust Score:</strong> 0.1472</div>
                <div class="metric">⭐ <strong>Performance Score:</strong> 0.0550</div>
            </div>

            <div class="section">
                <h2>🔒 LOCKED GRID TRADING CONDITIONS</h2>
                <div class="metric">🔒 <strong>BUY Condition:</strong> Enter at grid level, exit 1 level above</div>
                <div class="metric">🔒 <strong>SELL Condition:</strong> Enter at grid level, exit 1 level below</div>
                <div class="metric">🔒 <strong>HOLD Condition:</strong> Do nothing when confidence low</div>
                <div class="metric">🔒 <strong>Grid Spacing:</strong> 0.125% (LOCKED)</div>
                <div class="metric">🔒 <strong>Risk/Reward:</strong> 2:1 ratio (LOCKED)</div>
                <div class="metric">🔒 <strong>Take Profit:</strong> 0.125% (1 grid level)</div>
                <div class="metric">🔒 <strong>Stop Loss:</strong> 0.0625% (0.5 grid level)</div>
            </div>

            <div class="section">
                <h2>📊 ALL MODELS COMPARISON</h2>
                <table>
                    <tr>
                        <th>Model ID</th>
                        <th>Trades</th>
                        <th>Win Rate</th>
                        <th>P&L</th>
                        <th>Robust Score</th>
                        <th>Performance</th>
                        <th>Status</th>
                    </tr>
        
                    <tr>
                        <td>tcn_cnn_ppo_20250607_181319</td>
                        <td>15751</td>
                        <td>37.36%</td>
                        <td>$-0.93</td>
                        <td>0.1472</td>
                        <td>0.0550</td>
                        <td>🏆 BEST</td>
                    </tr>
            
                    <tr>
                        <td>tcn_cnn_ppo_20250607_181332</td>
                        <td>15751</td>
                        <td>37.36%</td>
                        <td>$-0.93</td>
                        <td>0.1472</td>
                        <td>0.0550</td>
                        <td>✅ SAVED</td>
                    </tr>
            
                    <tr>
                        <td>tcn_cnn_ppo_20250607_181343</td>
                        <td>15751</td>
                        <td>37.36%</td>
                        <td>$-0.93</td>
                        <td>0.1472</td>
                        <td>0.0550</td>
                        <td>✅ SAVED</td>
                    </tr>
            
                    <tr>
                        <td>tcn_cnn_ppo_20250607_181353</td>
                        <td>15751</td>
                        <td>37.36%</td>
                        <td>$-0.93</td>
                        <td>0.1472</td>
                        <td>0.0550</td>
                        <td>✅ SAVED</td>
                    </tr>
            
                    <tr>
                        <td>tcn_cnn_ppo_20250607_181403</td>
                        <td>15751</td>
                        <td>37.36%</td>
                        <td>$-0.93</td>
                        <td>0.1472</td>
                        <td>0.0550</td>
                        <td>✅ SAVED</td>
                    </tr>
            
                </table>
            </div>

            <div class="section">
                <h2>🎯 TARGET VALIDATION</h2>
                <div class="metric">🎯 <strong>Win Rate Target:</strong> ≥90%
                    <span class="warning">
                        ⚠️ NEEDS OPTIMIZATION
                    </span>
                </div>
                <div class="metric">📊 <strong>Robust Score Target:</strong> ≥0.79
                    <span class="warning">
                        ⚠️ NEEDS OPTIMIZATION
                    </span>
                </div>
                <div class="metric">🔒 <strong>Model Saved:</strong> ✅ Best model saved to saved_models/best_model.json</div>
            </div>

            <div class="section">
                <h2>🔒 DEPLOYMENT STATUS</h2>
                <div class="metric">
                    <strong>🚀 DEPLOYMENT READY:</strong>
                    <span class="warning">
                        ⚠️ NEEDS MORE OPTIMIZATION
                    </span>
                </div>
            </div>
        </body>
        </html>
        