#!/usr/bin/env python3
"""
COMPREHENSIVE TCN-CNN-PPO TRADING SYSTEM
Advanced ML-driven trading with complete indicator suite and robust metrics
"""

import ccxt
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import time
import threading
from collections import defaultdict, deque
import logging
import warnings
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import gym
from gym import spaces
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import BaseCallback

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tcn_cnn_ppo_trading.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TemporalConvNet(nn.Module):
    """Temporal Convolutional Network for time series analysis"""
    
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, 
                                   stride=1, dilation=dilation_size, 
                                   padding=(kernel_size-1) * dilation_size, 
                                   dropout=dropout)]
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)

class TemporalBlock(nn.Module):
    """Temporal Block for TCN"""
    
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        
        self.conv1 = nn.Conv1d(n_inputs, n_outputs, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(n_outputs, n_outputs, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        self.net = nn.Sequential(self.conv1, self.chomp1, self.relu1, self.dropout1,
                                self.conv2, self.chomp2, self.relu2, self.dropout2)
        
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        
    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class Chomp1d(nn.Module):
    """Remove padding from convolution"""
    
    def __init__(self, chomp_size):
        super(Chomp1d, self).__init__()
        self.chomp_size = chomp_size
    
    def forward(self, x):
        return x[:, :, :-self.chomp_size].contiguous()

class CNNFeatureExtractor(nn.Module):
    """CNN for spatial feature extraction"""
    
    def __init__(self, input_channels, output_size=64):
        super(CNNFeatureExtractor, self).__init__()
        
        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(32),
            nn.Dropout(0.2),
            
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Dropout(0.2),
            
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Dropout(0.2),
            
            nn.AdaptiveAvgPool1d(1)
        )
        
        self.fc = nn.Linear(128, output_size)
    
    def forward(self, x):
        x = self.conv_layers(x)
        x = x.view(x.size(0), -1)
        return self.fc(x)

class TCN_CNN_Model(nn.Module):
    """Combined TCN-CNN model for trading signal generation"""
    
    def __init__(self, num_features, sequence_length, num_actions=3):
        super(TCN_CNN_Model, self).__init__()
        
        # TCN for temporal patterns
        self.tcn = TemporalConvNet(
            num_inputs=num_features,
            num_channels=[64, 128, 256, 128, 64],
            kernel_size=3,
            dropout=0.2
        )
        
        # CNN for feature extraction
        self.cnn = CNNFeatureExtractor(num_features, output_size=64)
        
        # Combined feature processing
        self.feature_combiner = nn.Sequential(
            nn.Linear(64 + 64, 128),  # TCN + CNN features
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Output layers
        self.action_head = nn.Linear(64, num_actions)  # BUY, SELL, HOLD
        self.value_head = nn.Linear(64, 1)  # Value estimation
        self.confidence_head = nn.Linear(64, 1)  # Confidence score
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, num_features)
        x = x.transpose(1, 2)  # (batch_size, num_features, sequence_length)
        
        # TCN processing
        tcn_out = self.tcn(x)
        tcn_features = F.adaptive_avg_pool1d(tcn_out, 1).squeeze(-1)
        
        # CNN processing
        cnn_features = self.cnn(x)
        
        # Combine features
        combined = torch.cat([tcn_features, cnn_features], dim=1)
        features = self.feature_combiner(combined)
        
        # Generate outputs
        actions = F.softmax(self.action_head(features), dim=1)
        value = self.value_head(features)
        confidence = torch.sigmoid(self.confidence_head(features))
        
        return actions, value, confidence

class LockedIndicatorCalculator:
    """🔒 LOCKED INDICATOR CALCULATOR - EXACTLY 4 INDICATORS ONLY"""

    @staticmethod
    def calculate_locked_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """🔒 Calculate ONLY the 4 locked indicators - NO DEVIATIONS"""

        # Basic price data
        close = df['close'].values
        volume = df['volume'].values

        # 🔒 INDICATOR 1: VWAP (Volume Weighted Average Price) - 20 period
        df['vwap'] = (close * volume).rolling(20).sum() / volume.rolling(20).sum()
        df['vwap_ratio'] = df['vwap'] / close

        # 🔒 INDICATOR 2: Bollinger Bands Position - 20 period, 2 std
        df['bb_middle'] = close.rolling(20).mean()
        df['bb_std'] = close.rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (close - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

        # 🔒 INDICATOR 3: RSI - 14 period (normalized 0-1)
        df['rsi_14'] = LockedIndicatorCalculator._calculate_rsi(close, 14) / 100.0

        # 🔒 INDICATOR 4: ETH/BTC Ratio (will be added externally)
        # This is calculated separately in the main system

        return df
    
    @staticmethod
    def _calculate_rsi(prices, period):
        """Calculate RSI"""
        delta = pd.Series(prices).diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def _calculate_atr(high, low, close, period):
        """Calculate Average True Range"""
        high_low = high - low
        high_close = np.abs(high - np.roll(close, 1))
        low_close = np.abs(low - np.roll(close, 1))
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return pd.Series(true_range).rolling(period).mean()

class RobustMetricsCalculator:
    """Calculate robust performance metrics for model evaluation"""
    
    @staticmethod
    def calculate_robust_score(returns: np.ndarray, equity_curve: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive robust score"""
        
        # Sortino Ratio (normalized)
        sortino_ratio = RobustMetricsCalculator._calculate_sortino_ratio(returns)
        sortino_norm = min(1.0, max(0.0, sortino_ratio / 3.0))  # Normalize to 0-1
        
        # Ulcer Index (inverted and normalized)
        ulcer_index = RobustMetricsCalculator._calculate_ulcer_index(equity_curve)
        ulcer_index_inv = 1.0 / (1.0 + ulcer_index)  # Invert (lower is better)
        
        # Equity Curve R²
        x = np.arange(len(equity_curve))
        equity_curve_r2 = r2_score(equity_curve, np.polyval(np.polyfit(x, equity_curve, 1), x))
        equity_curve_r2 = max(0.0, min(1.0, equity_curve_r2))  # Clamp to 0-1
        
        # Profit Stability
        profit_stability = RobustMetricsCalculator._calculate_profit_stability(returns)
        
        # Upward Move Ratio
        upward_move_ratio = RobustMetricsCalculator._calculate_upward_move_ratio(equity_curve)
        
        # Drawdown Duration (inverted)
        drawdown_duration = RobustMetricsCalculator._calculate_avg_drawdown_duration(equity_curve)
        drawdown_duration_inv = 1.0 / (1.0 + drawdown_duration / 100.0)  # Normalize
        
        # 🔒 LOCKED ROBUST SCORE FORMULA - EXACT WEIGHTS - NO DEVIATIONS
        robust_score = (
            0.25 * sortino_norm +           # 25% - Risk-adjusted returns
            0.20 * ulcer_index_inv +        # 20% - Downside volatility (inverted)
            0.15 * equity_curve_r2 +        # 15% - Equity curve smoothness
            0.15 * profit_stability +       # 15% - Consistent profitability
            0.15 * upward_move_ratio +      # 15% - Upward momentum ratio
            0.10 * drawdown_duration_inv    # 10% - Recovery speed (inverted)
        )
        # 🔒 CRITICAL: THESE WEIGHTS ARE LOCKED - NO MODIFICATIONS PERMITTED
        
        return {
            'robust_score': robust_score,
            'sortino_norm': sortino_norm,
            'ulcer_index_inv': ulcer_index_inv,
            'equity_curve_r2': equity_curve_r2,
            'profit_stability': profit_stability,
            'upward_move_ratio': upward_move_ratio,
            'drawdown_duration_inv': drawdown_duration_inv,
            'sortino_ratio': sortino_ratio,
            'ulcer_index': ulcer_index,
            'drawdown_duration': drawdown_duration
        }
    
    @staticmethod
    def _calculate_sortino_ratio(returns: np.ndarray, target_return: float = 0.0) -> float:
        """Calculate Sortino ratio"""
        excess_returns = returns - target_return
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return 3.0  # Maximum normalized value
        
        downside_deviation = np.sqrt(np.mean(downside_returns ** 2))
        if downside_deviation == 0:
            return 3.0
        
        return np.mean(excess_returns) / downside_deviation
    
    @staticmethod
    def _calculate_ulcer_index(equity_curve: np.ndarray) -> float:
        """Calculate Ulcer Index"""
        running_max = np.maximum.accumulate(equity_curve)
        drawdowns = (equity_curve - running_max) / running_max * 100
        squared_drawdowns = drawdowns ** 2
        return np.sqrt(np.mean(squared_drawdowns))
    
    @staticmethod
    def _calculate_profit_stability(returns: np.ndarray) -> float:
        """Calculate profit stability (consistency of returns)"""
        if len(returns) == 0:
            return 0.0
        
        # Calculate rolling mean and std of returns
        window = min(30, len(returns) // 4)
        if window < 5:
            return 0.0
        
        rolling_mean = pd.Series(returns).rolling(window).mean()
        rolling_std = pd.Series(returns).rolling(window).std()
        
        # Stability is inverse of coefficient of variation
        cv = rolling_std / (np.abs(rolling_mean) + 1e-8)
        stability = 1.0 / (1.0 + np.nanmean(cv))
        
        return min(1.0, max(0.0, stability))
    
    @staticmethod
    def _calculate_upward_move_ratio(equity_curve: np.ndarray) -> float:
        """Calculate ratio of upward moves in equity curve"""
        if len(equity_curve) < 2:
            return 0.0
        
        moves = np.diff(equity_curve)
        upward_moves = np.sum(moves > 0)
        total_moves = len(moves)
        
        return upward_moves / total_moves if total_moves > 0 else 0.0
    
    @staticmethod
    def _calculate_avg_drawdown_duration(equity_curve: np.ndarray) -> float:
        """Calculate average drawdown duration in periods"""
        if len(equity_curve) < 2:
            return 0.0
        
        running_max = np.maximum.accumulate(equity_curve)
        is_drawdown = equity_curve < running_max
        
        # Find drawdown periods
        drawdown_periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, is_dd in enumerate(is_drawdown):
            if is_dd and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif not is_dd and in_drawdown:
                in_drawdown = False
                drawdown_periods.append(i - start_idx)
        
        # Handle case where we end in drawdown
        if in_drawdown:
            drawdown_periods.append(len(equity_curve) - start_idx)
        
        return np.mean(drawdown_periods) if drawdown_periods else 0.0

class TradingEnvironment(gym.Env):
    """Advanced trading environment for PPO training"""

    def __init__(self, data: pd.DataFrame, initial_balance: float = 10000.0,
                 sequence_length: int = 60, max_trades_per_day: int = 10):
        super(TradingEnvironment, self).__init__()

        # 🔒 LOCKED: VALIDATE REAL DATA INPUT ONLY
        if data.empty:
            raise ValueError("🔒 CRITICAL: No real data provided to trading environment")

        # 🔒 VALIDATION: Ensure data has required OHLCV columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"🔒 CRITICAL: Missing real data columns: {missing_columns}")

        self.data = data.copy()
        self.initial_balance = initial_balance
        self.sequence_length = sequence_length
        self.max_trades_per_day = max_trades_per_day

        # 🔒 Calculate ONLY the 4 locked indicators on REAL data
        self.data = LockedIndicatorCalculator.calculate_locked_indicators(self.data)

        logging.info(f"🔒 REAL DATA ENVIRONMENT: Initialized with {len(self.data)} real data points")

        # 🔒 LOCKED FEATURE COLUMNS - EXACTLY 4 INDICATORS - NO DEVIATIONS
        self.feature_columns = [
            'vwap_ratio',      # VWAP / Close price ratio
            'bb_position',     # Position within Bollinger Bands (0-1)
            'rsi_14',          # 14-period RSI (normalized 0-1)
            'eth_btc_ratio'    # ETH/BTC market correlation ratio
        ]
        # 🔒 CRITICAL: NO OTHER INDICATORS PERMITTED - SYSTEM LOCKED

        # Normalize features
        self.scaler = StandardScaler()
        feature_data = self.data[self.feature_columns].fillna(0)
        self.normalized_features = self.scaler.fit_transform(feature_data)

        # Action space: 0=HOLD, 1=BUY, 2=SELL
        self.action_space = spaces.Discrete(3)

        # Observation space: sequence of normalized features
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(self.sequence_length, len(self.feature_columns)),
            dtype=np.float32
        )

        self.reset()

    def reset(self):
        """Reset environment to initial state"""
        self.current_step = self.sequence_length
        self.balance = self.initial_balance
        self.position = 0  # 0=no position, 1=long, -1=short
        self.entry_price = 0
        self.trades_today = 0
        self.daily_trades_count = 0
        self.last_trade_day = -1

        # Performance tracking
        self.equity_curve = [self.initial_balance]
        self.returns_history = []
        self.trade_history = []

        return self._get_observation()

    def step(self, action):
        """Execute one step in the environment"""
        current_price = self.data.iloc[self.current_step]['close']
        current_day = self.current_step // (24 * 60)  # Assuming 1-minute data

        # Reset daily trade count if new day
        if current_day != self.last_trade_day:
            self.daily_trades_count = 0
            self.last_trade_day = current_day

        reward = 0
        done = False
        info = {}

        # Execute action
        if action == 1 and self.position == 0 and self.daily_trades_count < self.max_trades_per_day:
            # BUY
            self.position = 1
            self.entry_price = current_price
            self.daily_trades_count += 1
            info['action'] = 'BUY'

        elif action == 2 and self.position == 0 and self.daily_trades_count < self.max_trades_per_day:
            # SELL
            self.position = -1
            self.entry_price = current_price
            self.daily_trades_count += 1
            info['action'] = 'SELL'

        elif action == 0 and self.position != 0:
            # CLOSE POSITION
            if self.position == 1:  # Close long
                pnl = (current_price - self.entry_price) / self.entry_price
            else:  # Close short
                pnl = (self.entry_price - current_price) / self.entry_price

            # Apply transaction costs (0.1%)
            pnl -= 0.001

            # Update balance
            trade_value = self.balance * 0.1  # Risk 10% per trade
            self.balance += trade_value * pnl

            # Record trade
            self.trade_history.append({
                'entry_price': self.entry_price,
                'exit_price': current_price,
                'pnl': pnl,
                'position': self.position,
                'step': self.current_step
            })

            # Calculate reward based on PnL and robust metrics
            reward = self._calculate_reward(pnl)

            self.position = 0
            self.entry_price = 0
            info['action'] = 'CLOSE'
            info['pnl'] = pnl

        # Update equity curve
        current_equity = self.balance
        if self.position != 0:
            # Mark-to-market unrealized PnL
            if self.position == 1:
                unrealized_pnl = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl = (self.entry_price - current_price) / self.entry_price

            current_equity += self.balance * 0.1 * unrealized_pnl

        self.equity_curve.append(current_equity)

        # Calculate returns
        if len(self.equity_curve) > 1:
            period_return = (current_equity - self.equity_curve[-2]) / self.equity_curve[-2]
            self.returns_history.append(period_return)

        # Move to next step
        self.current_step += 1

        # Check if episode is done
        if self.current_step >= len(self.data) - 1:
            done = True
            # Calculate final robust score
            if len(self.returns_history) > 10:
                metrics = RobustMetricsCalculator.calculate_robust_score(
                    np.array(self.returns_history),
                    np.array(self.equity_curve)
                )
                reward += metrics['robust_score'] * 10  # Bonus for good overall performance
                info['final_metrics'] = metrics

        # Penalty for excessive drawdown
        if current_equity < self.initial_balance * 0.8:  # 20% drawdown
            reward -= 5
            done = True
            info['reason'] = 'excessive_drawdown'

        observation = self._get_observation()

        return observation, reward, done, info

    def _get_observation(self):
        """Get current observation (sequence of features)"""
        start_idx = max(0, self.current_step - self.sequence_length)
        end_idx = self.current_step

        # Get feature sequence
        if start_idx == 0 and end_idx < self.sequence_length:
            # Pad with first observation if not enough history
            obs = np.zeros((self.sequence_length, len(self.feature_columns)))
            available_data = self.normalized_features[start_idx:end_idx]
            obs[-len(available_data):] = available_data
        else:
            obs = self.normalized_features[start_idx:end_idx]

        return obs.astype(np.float32)

    def _calculate_reward(self, pnl: float) -> float:
        """Calculate reward based on PnL and risk metrics"""
        # Base reward from PnL
        reward = pnl * 100  # Scale up

        # Bonus for profitable trades
        if pnl > 0:
            reward += 1
        else:
            reward -= 1

        # Penalty for large losses
        if pnl < -0.02:  # More than 2% loss
            reward -= 5

        # Bonus for consistent performance
        if len(self.returns_history) > 20:
            recent_returns = np.array(self.returns_history[-20:])
            if np.mean(recent_returns) > 0 and np.std(recent_returns) < 0.01:
                reward += 2  # Bonus for consistent positive returns

        return reward

class ETHBTCRatioCalculator:
    """Calculate ETH/BTC ratio for market correlation analysis"""

    def __init__(self, exchange):
        self.exchange = exchange

    def get_eth_btc_ratio(self) -> float:
        """Get current ETH/BTC ratio"""
        try:
            eth_ticker = self.exchange.fetch_ticker('ETH/USDT')
            btc_ticker = self.exchange.fetch_ticker('BTC/USDT')

            eth_price = eth_ticker['last']
            btc_price = btc_ticker['last']

            ratio = eth_price / btc_price
            return ratio

        except Exception as e:
            logging.error(f"Error fetching ETH/BTC ratio: {e}")
            return 0.065  # Default ratio

    def get_ratio_history(self, hours: int = 24) -> List[float]:
        """Get historical ETH/BTC ratios"""
        try:
            since = int((datetime.now() - timedelta(hours=hours)).timestamp() * 1000)

            eth_ohlcv = self.exchange.fetch_ohlcv('ETH/USDT', '1h', since=since)
            btc_ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1h', since=since)

            ratios = []
            for eth_candle, btc_candle in zip(eth_ohlcv, btc_ohlcv):
                eth_close = eth_candle[4]  # Close price
                btc_close = btc_candle[4]  # Close price
                ratios.append(eth_close / btc_close)

            return ratios

        except Exception as e:
            logging.error(f"Error fetching ETH/BTC ratio history: {e}")
            return [0.065] * hours  # Default ratios

class TCN_CNN_PPO_TradingSystem:
    """Complete TCN-CNN-PPO Trading System"""

    def __init__(self, api_key_file: str = "BinanceAPI_2.txt", initial_balance: float = 300.0):
        self.api_key_file = api_key_file
        self.exchange = None
        self.initial_balance = initial_balance
        self.current_balance = initial_balance

        # 🔒 LOCKED MODEL PARAMETERS
        self.sequence_length = 60
        self.num_features = 4  # 🔒 EXACTLY 4 INDICATORS - LOCKED
        self.model = None
        self.ppo_agent = None
        self.scaler = StandardScaler()

        # 🔒 LOCKED: REAL DATA VALIDATION
        self.real_data_validation = {
            'total_data_points': 0,
            'last_data_timestamp': None,
            'data_source_verified': False,
            'binance_connection_verified': False,
            'real_data_only': True  # 🔒 LOCKED: NO SIMULATED DATA
        }

        # Trading state
        self.open_trades = []
        self.completed_trades = []
        self.equity_curve = [initial_balance]
        self.is_running = False
        self.last_update = datetime.now()

        # Performance tracking
        self.daily_stats = defaultdict(dict)
        self.total_trades = 0
        self.winning_trades = 0

        # ETH/BTC ratio calculator
        self.eth_btc_calculator = None

        # 🔒 LOCKED: Real data storage only
        self.real_price_history = deque(maxlen=1000)
        self.real_feature_history = deque(maxlen=1000)

        self._connect_exchange()
        self._validate_real_data_source()
        self._initialize_models()

    def _validate_real_data_source(self):
        """🔒 LOCKED: Validate that ONLY real Binance data is used"""
        try:
            if not self.exchange:
                logging.error("🔒 CRITICAL: No Binance exchange connection for real data")
                raise Exception("Real data source validation failed")

            # Test real data connection
            test_ticker = self.exchange.fetch_ticker('BTC/USDT')
            if not test_ticker or 'last' not in test_ticker:
                logging.error("🔒 CRITICAL: Cannot fetch real Binance ticker data")
                raise Exception("Real ticker data validation failed")

            # Test real OHLCV data
            test_ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1m', limit=5)
            if not test_ohlcv or len(test_ohlcv) == 0:
                logging.error("🔒 CRITICAL: Cannot fetch real Binance OHLCV data")
                raise Exception("Real OHLCV data validation failed")

            # Validate data freshness (within last 5 minutes)
            latest_timestamp = test_ohlcv[-1][0]
            current_time = int(datetime.now().timestamp() * 1000)
            time_diff = current_time - latest_timestamp

            if time_diff > 300000:  # 5 minutes in milliseconds
                logging.error(f"🔒 CRITICAL: Real data is stale ({time_diff/1000:.0f} seconds old)")
                raise Exception("Real data freshness validation failed")

            # Update validation status
            self.real_data_validation.update({
                'data_source_verified': True,
                'binance_connection_verified': True,
                'last_data_timestamp': latest_timestamp,
                'validation_time': datetime.now().isoformat()
            })

            logging.info("🔒 REAL DATA VALIDATION: All checks passed - ONLY real Binance data will be used")

        except Exception as e:
            logging.error(f"🔒 CRITICAL: Real data validation failed: {e}")
            raise

    def _connect_exchange(self):
        """Connect to Binance"""
        try:
            with open(self.api_key_file, 'r') as f:
                lines = f.read().strip().split('\n')
                api_key = lines[0].strip()
                secret_key = lines[1].strip()

            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,
                'enableRateLimit': True,
            })

            self.exchange.load_markets()
            self.eth_btc_calculator = ETHBTCRatioCalculator(self.exchange)

            logging.info("CONNECTED to Binance for TCN-CNN-PPO TRADING")

            # Test connection
            balance = self.exchange.fetch_balance()
            usdt_balance = balance['USDT']['free']
            logging.info(f"USDT Balance: ${usdt_balance:.2f}")

        except Exception as e:
            logging.error(f"Failed to connect to Binance: {e}")
            raise

    def _initialize_models(self):
        """Initialize TCN-CNN-PPO models"""
        try:
            # Initialize TCN-CNN model
            self.model = TCN_CNN_Model(
                num_features=self.num_features,
                sequence_length=self.sequence_length,
                num_actions=3
            )

            # Try to load pre-trained model
            try:
                checkpoint = torch.load('tcn_cnn_model.pth', map_location='cpu')
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.scaler = checkpoint['scaler']
                logging.info("Loaded pre-trained TCN-CNN model")
            except FileNotFoundError:
                logging.info("No pre-trained model found, using random initialization")

            # Initialize PPO agent
            self._initialize_ppo_agent()

        except Exception as e:
            logging.error(f"Error initializing models: {e}")
            raise

    def _initialize_ppo_agent(self):
        """Initialize PPO agent"""
        try:
            # Create dummy environment for PPO initialization
            dummy_data = pd.DataFrame({
                'open': [100] * 100,
                'high': [101] * 100,
                'low': [99] * 100,
                'close': [100] * 100,
                'volume': [1000] * 100
            })

            env = TradingEnvironment(dummy_data, sequence_length=self.sequence_length)
            env = DummyVecEnv([lambda: env])

            # Initialize PPO
            self.ppo_agent = PPO(
                "MlpPolicy",
                env,
                learning_rate=3e-4,
                n_steps=2048,
                batch_size=64,
                n_epochs=10,
                gamma=0.99,
                gae_lambda=0.95,
                clip_range=0.2,
                verbose=0
            )

            # Try to load pre-trained PPO model
            try:
                self.ppo_agent.load("ppo_trading_model")
                logging.info("Loaded pre-trained PPO model")
            except FileNotFoundError:
                logging.info("No pre-trained PPO model found, using random initialization")

        except Exception as e:
            logging.error(f"Error initializing PPO agent: {e}")

    def get_real_binance_data(self, hours: int = 25) -> pd.DataFrame:
        """🔒 LOCKED: Get ONLY real Binance data - NO SIMULATED DATA PERMITTED"""
        try:
            # 🔒 LOCKED: ONLY REAL BINANCE API DATA
            since = int((datetime.now() - timedelta(hours=hours)).timestamp() * 1000)

            # 🔒 CRITICAL: REAL BINANCE DATA ONLY - NO EXCEPTIONS
            ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1m', since=since, limit=hours*60)

            if not ohlcv:
                logging.error("🔒 CRITICAL: No real Binance data received")
                return pd.DataFrame()

            # 🔒 LOCKED: Real market data processing
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('datetime')

            # Validate real data
            if len(df) == 0:
                logging.error("🔒 CRITICAL: Empty real Binance data")
                return pd.DataFrame()

            logging.info(f"🔒 REAL BINANCE DATA: Collected {len(df)} real market data points")

            # 🔒 Calculate ONLY the 4 locked indicators on REAL data
            df = LockedIndicatorCalculator.calculate_locked_indicators(df)

            # 🔒 Add REAL ETH/BTC ratio from Binance
            try:
                real_eth_btc_ratio = self.eth_btc_calculator.get_eth_btc_ratio()
                df['eth_btc_ratio'] = real_eth_btc_ratio
                logging.info(f"🔒 REAL ETH/BTC RATIO: {real_eth_btc_ratio:.6f}")
            except Exception as e:
                logging.error(f"🔒 ERROR: Failed to get real ETH/BTC ratio: {e}")
                return pd.DataFrame()

            # 🔒 VALIDATION: Ensure all data is real and complete
            if df.isnull().any().any():
                logging.warning("🔒 WARNING: NaN values in real data, filling...")
                df = df.bfill().fillna(0)

            logging.info("🔒 REAL DATA VALIDATED: All indicators calculated on real Binance data")
            return df

        except Exception as e:
            logging.error(f"🔒 CRITICAL ERROR: Failed to fetch real Binance data: {e}")
            return pd.DataFrame()

    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """🔒 Prepare features using ONLY the 4 locked indicators"""
        # 🔒 LOCKED FEATURE COLUMNS - EXACTLY 4 INDICATORS - NO DEVIATIONS
        feature_columns = [
            'vwap_ratio',      # VWAP / Close price ratio
            'bb_position',     # Position within Bollinger Bands (0-1)
            'rsi_14',          # 14-period RSI (normalized 0-1)
            'eth_btc_ratio'    # ETH/BTC market correlation ratio
        ]
        # 🔒 CRITICAL: NO OTHER INDICATORS PERMITTED - SYSTEM LOCKED

        # Fill missing values
        feature_data = df[feature_columns].bfill().fillna(0)

        # Normalize features
        if hasattr(self.scaler, 'mean_'):
            # Use existing scaler
            normalized_features = self.scaler.transform(feature_data)
        else:
            # Fit new scaler
            normalized_features = self.scaler.fit_transform(feature_data)

        return normalized_features

    def generate_signal(self, current_price: float, df: pd.DataFrame) -> Tuple[Optional[str], float, Dict]:
        """Generate trading signal using TCN-CNN-PPO"""
        try:
            if df.empty or len(df) < self.sequence_length:
                return None, 0.0, {}

            # Prepare features
            features = self.prepare_features(df)

            if len(features) < self.sequence_length:
                return None, 0.0, {}

            # Get latest sequence
            sequence = features[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            sequence_tensor = torch.FloatTensor(sequence)

            # Get model predictions
            with torch.no_grad():
                actions, value, confidence = self.model(sequence_tensor)
                action_probs = actions.squeeze().numpy()
                confidence_score = confidence.squeeze().item()
                value_estimate = value.squeeze().item()

            # Get PPO action if available
            ppo_action = None
            if self.ppo_agent is not None:
                try:
                    obs = sequence.squeeze()
                    ppo_action, _ = self.ppo_agent.predict(obs, deterministic=True)
                except:
                    pass

            # Determine final action
            tcn_cnn_action = np.argmax(action_probs)
            final_action = ppo_action if ppo_action is not None else tcn_cnn_action

            # Convert to trading signal
            if final_action == 1 and confidence_score > 0.8:  # BUY
                direction = "BUY"
            elif final_action == 2 and confidence_score > 0.8:  # SELL
                direction = "SELL"
            else:
                direction = None

            signal_info = {
                'tcn_cnn_action': tcn_cnn_action,
                'ppo_action': ppo_action,
                'final_action': final_action,
                'action_probs': action_probs.tolist(),
                'value_estimate': value_estimate,
                'confidence_score': confidence_score,
                'model_agreement': tcn_cnn_action == ppo_action if ppo_action is not None else True
            }

            return direction, confidence_score, signal_info

        except Exception as e:
            logging.error(f"Error generating signal: {e}")
            return None, 0.0, {}

    def calculate_position_size(self, price: float, confidence: float) -> float:
        """Calculate position size based on confidence and risk management"""
        base_risk = 10.0  # Base $10 risk

        # Dynamic scaling based on balance
        if self.current_balance > 1000:
            excess = self.current_balance - 1000
            additional_increments = int(excess // 500)
            base_risk += additional_increments * 10.0

        # Adjust based on confidence
        confidence_multiplier = min(1.5, confidence / 0.8)  # Scale with confidence
        adjusted_risk = base_risk * confidence_multiplier

        # Calculate position size for 0.125% stop loss
        stop_loss_pct = 0.00125  # 0.125%
        position_size = adjusted_risk / (price * stop_loss_pct)

        return position_size

    def place_order(self, direction: str, price: float, confidence: float, signal_info: Dict) -> Optional[Dict]:
        """Place trading order"""
        try:
            position_size = self.calculate_position_size(price, confidence)

            # Calculate exit levels (optimal 0.125% grid spacing)
            take_profit_pct = 0.00125  # 0.125%
            stop_loss_pct = 0.000625   # 0.0625%

            if direction == "BUY":
                take_profit_price = price * (1 + take_profit_pct)
                stop_loss_price = price * (1 - stop_loss_pct)
            else:  # SELL
                take_profit_price = price * (1 - take_profit_pct)
                stop_loss_price = price * (1 + stop_loss_pct)

            # Simulated order (for safety)
            order = {
                'id': f"tcn_cnn_ppo_{int(time.time())}",
                'symbol': 'BTC/USDT',
                'side': direction.lower(),
                'amount': position_size,
                'price': price,
                'status': 'filled',
                'timestamp': int(time.time() * 1000)
            }

            trade = {
                'id': order['id'],
                'direction': direction,
                'entry_price': price,
                'quantity': position_size,
                'take_profit_price': take_profit_price,
                'stop_loss_price': stop_loss_price,
                'entry_time': datetime.now(),
                'confidence': confidence,
                'signal_info': signal_info,
                'status': 'OPEN'
            }

            self.open_trades.append(trade)

            logging.info(f"TCN-CNN-PPO ORDER PLACED {direction}: ${price:,.2f} | "
                        f"Confidence: {confidence:.1%} | Size: {position_size:.6f}")
            logging.info(f"Signal Info: TCN-CNN={signal_info.get('tcn_cnn_action')}, "
                        f"PPO={signal_info.get('ppo_action')}, "
                        f"Agreement={signal_info.get('model_agreement')}")

            return trade

        except Exception as e:
            logging.error(f"Error placing order: {e}")
            return None

    def check_exits(self, current_price: float):
        """Check for trade exits"""
        for trade in self.open_trades[:]:
            direction = trade['direction']
            entry_price = trade['entry_price']

            profit_loss = 0
            exit_type = None

            if direction == "BUY":
                if current_price >= trade['take_profit_price']:
                    profit_loss = (trade['take_profit_price'] - entry_price) * trade['quantity']
                    exit_type = "TP"
                elif current_price <= trade['stop_loss_price']:
                    profit_loss = (trade['stop_loss_price'] - entry_price) * trade['quantity']
                    exit_type = "SL"

            else:  # SELL
                if current_price <= trade['take_profit_price']:
                    profit_loss = (entry_price - trade['take_profit_price']) * trade['quantity']
                    exit_type = "TP"
                elif current_price >= trade['stop_loss_price']:
                    profit_loss = (entry_price - trade['stop_loss_price']) * trade['quantity']
                    exit_type = "SL"

            if exit_type:
                # Close trade
                self.current_balance += profit_loss
                trade['exit_price'] = current_price
                trade['exit_time'] = datetime.now()
                trade['profit_loss'] = profit_loss
                trade['exit_type'] = exit_type
                trade['status'] = 'CLOSED'

                # Move to completed trades
                self.completed_trades.append(trade)
                self.open_trades.remove(trade)

                # Update statistics
                self.total_trades += 1
                if profit_loss > 0:
                    self.winning_trades += 1

                # Update equity curve
                self.equity_curve.append(self.current_balance)

                duration = (trade['exit_time'] - trade['entry_time']).total_seconds() / 3600
                logging.info(f"TCN-CNN-PPO TRADE CLOSED {exit_type} {direction}: "
                           f"${current_price:,.2f} | P&L: ${profit_loss:+.2f} | "
                           f"Duration: {duration:.1f}h")

    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        total_profit = self.current_balance - self.initial_balance
        total_return = (total_profit / self.initial_balance) * 100
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0

        # Calculate robust metrics if enough data
        robust_metrics = {}
        if len(self.equity_curve) > 20:
            returns = np.diff(self.equity_curve) / np.array(self.equity_curve[:-1])
            robust_metrics = RobustMetricsCalculator.calculate_robust_score(
                returns, np.array(self.equity_curve)
            )

        return {
            'initial_balance': self.initial_balance,
            'current_balance': self.current_balance,
            'total_profit': total_profit,
            'total_return_pct': total_return,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.total_trades - self.winning_trades,
            'win_rate': win_rate,
            'open_trades': len(self.open_trades),
            'last_update': self.last_update.isoformat(),
            'robust_metrics': robust_metrics
        }

    def trading_loop(self):
        """Main trading loop"""
        logging.info("STARTING TCN-CNN-PPO TRADING LOOP")

        while self.is_running:
            try:
                # Get current price
                ticker = self.exchange.fetch_ticker('BTC/USDT')
                current_price = ticker['last']

                # Check exits for open trades
                self.check_exits(current_price)

                # Generate new signals only if no open trades (Conservative Elite)
                if len(self.open_trades) == 0:
                    # 🔒 LOCKED: Get ONLY real Binance data
                    df = self.get_real_binance_data(100)  # Real data for indicators

                    if not df.empty:
                        direction, confidence, signal_info = self.generate_signal(current_price, df)

                        if direction and confidence > 0.8:
                            self.place_order(direction, current_price, confidence, signal_info)
                    else:
                        logging.error("🔒 CRITICAL: No real Binance data available for signal generation")

                # Update timestamp
                self.last_update = datetime.now()

                # Log status every 10 minutes
                if int(time.time()) % 600 == 0:
                    stats = self.get_performance_stats()
                    logging.info(f"TCN-CNN-PPO Status: Balance=${stats['current_balance']:.2f} | "
                               f"Profit=${stats['total_profit']:+.2f} | "
                               f"Trades={stats['total_trades']} | "
                               f"Win Rate={stats['win_rate']:.1f}%")

                    if stats['robust_metrics']:
                        robust_score = stats['robust_metrics']['robust_score']
                        logging.info(f"Robust Score: {robust_score:.3f}")

                # Sleep for 1 minute
                time.sleep(60)

            except Exception as e:
                logging.error(f"Error in trading loop: {e}")
                time.sleep(60)

    def start_trading(self):
        """Start the trading system"""
        if self.is_running:
            logging.warning("TCN-CNN-PPO trading system already running")
            return

        self.is_running = True

        # Start trading in separate thread
        trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        trading_thread.start()

        logging.info("TCN-CNN-PPO TRADING SYSTEM STARTED")

    def stop_trading(self):
        """Stop the trading system"""
        self.is_running = False
        logging.info("TCN-CNN-PPO TRADING SYSTEM STOPPED")

    def save_state(self):
        """Save current state and models"""
        state = {
            'current_balance': self.current_balance,
            'completed_trades': self.completed_trades,
            'equity_curve': self.equity_curve,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'last_update': self.last_update.isoformat()
        }

        with open('tcn_cnn_ppo_state.json', 'w') as f:
            json.dump(state, f, indent=2, default=str)

        # Save models
        if self.model is not None:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'scaler': self.scaler
            }, 'tcn_cnn_model.pth')

        if self.ppo_agent is not None:
            self.ppo_agent.save("ppo_trading_model")

        logging.info("TCN-CNN-PPO state and models saved")

def main():
    """Main function for standalone operation"""
    logging.info("INITIALIZING TCN-CNN-PPO TRADING SYSTEM")

    # Create trading system
    trader = TCN_CNN_PPO_TradingSystem(initial_balance=300.0)

    # Start trading
    trader.start_trading()

    try:
        # Keep running
        while True:
            time.sleep(300)  # Save state every 5 minutes
            trader.save_state()

    except KeyboardInterrupt:
        logging.info("Shutdown signal received")
        trader.stop_trading()
        trader.save_state()

if __name__ == "__main__":
    main()
