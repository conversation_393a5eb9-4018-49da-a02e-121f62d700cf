# TCN-CNN-PPO Advanced Trading System Requirements
# Install with: pip install -r requirements_tcn_cnn_ppo.txt

# Core trading dependencies
ccxt>=4.0.0
pandas>=1.5.0
numpy>=1.24.0

# Deep Learning frameworks
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Reinforcement Learning
stable-baselines3>=2.0.0
gym>=0.26.0
gymnasium>=0.28.0

# Machine Learning utilities
scikit-learn>=1.3.0
scipy>=1.10.0

# Technical Analysis
TA-Lib>=0.4.25

# Web application
Flask>=2.3.0

# Data processing and visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Utilities
python-dateutil>=2.8.0
tqdm>=4.65.0
joblib>=1.3.0

# Optional: GPU acceleration (uncomment if using CUDA)
# torch-audio>=2.0.0+cu118
# torch-vision>=0.15.0+cu118

# Development dependencies (optional)
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
jupyter>=1.0.0
