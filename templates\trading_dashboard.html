<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCN-CNN-PPO Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .model-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .model-stat {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .model-stat .label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .model-stat .value {
            font-size: 1.2em;
            font-weight: bold;
            color: #ffd700;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 30px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn.stop {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 5px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        .status-item .label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .status-item .value {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .profit {
            color: #28a745;
        }
        
        .loss {
            color: #dc3545;
        }
        
        .neutral {
            color: #ffd700;
        }
        
        .price-display {
            text-align: center;
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        .trades-section {
            grid-column: 1 / -1;
        }
        
        .trades-tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        
        .tab.active {
            background: rgba(255, 255, 255, 0.3);
            color: #ffd700;
        }
        
        .trades-table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .trades-table th,
        .trades-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .trades-table th {
            background: rgba(255, 255, 255, 0.2);
            font-weight: bold;
            color: #ffd700;
        }
        
        .trades-table tr:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-running {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .model-info {
                grid-template-columns: 1fr 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TCN-CNN-PPO Trading Dashboard</h1>
            <p>Best Profit Ensemble Model - Live Trading Simulation</p>
            
            <div class="model-info">
                <div class="model-stat">
                    <div class="label">Model ID</div>
                    <div class="value" id="model-id">Loading...</div>
                </div>
                <div class="model-stat">
                    <div class="label">Target Profit</div>
                    <div class="value" id="target-profit">Loading...</div>
                </div>
                <div class="model-stat">
                    <div class="label">Target Win Rate</div>
                    <div class="value" id="target-winrate">Loading...</div>
                </div>
                <div class="model-stat">
                    <div class="label">Trades/Day</div>
                    <div class="value" id="target-trades">Loading...</div>
                </div>
                <div class="model-stat">
                    <div class="label">Composite Score</div>
                    <div class="value" id="composite-score">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" id="start-btn" onclick="startTrading()">▶️ Start Trading</button>
            <button class="btn stop" id="stop-btn" onclick="stopTrading()">⏹️ Stop Trading</button>
            <span class="status-indicator" id="status-indicator"></span>
            <span id="status-text">Stopped</span>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>📈 Market Price</h3>
                <div class="price-display" id="current-price">$0.00</div>
                <div style="text-align: center; opacity: 0.8;">BTC/USD</div>
            </div>
            
            <div class="card">
                <h3>📊 Performance</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="label">Equity</div>
                        <div class="value" id="equity">$0.00</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Total P&L</div>
                        <div class="value" id="total-pnl">$0.00</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Win Rate</div>
                        <div class="value" id="win-rate">0%</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Total Trades</div>
                        <div class="value" id="total-trades">0</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Daily Trades</div>
                        <div class="value" id="daily-trades">0</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Open Positions</div>
                        <div class="value" id="open-positions">0</div>
                    </div>
                </div>
            </div>
            
            <div class="card trades-section">
                <h3>📋 Trading Activity</h3>
                
                <div class="trades-tabs">
                    <button class="tab active" onclick="showTab('recent')">Recent Trades</button>
                    <button class="tab" onclick="showTab('open')">Open Positions</button>
                </div>
                
                <div id="recent-trades-tab">
                    <table class="trades-table">
                        <thead>
                            <tr>
                                <th>Trade ID</th>
                                <th>Direction</th>
                                <th>Entry Price</th>
                                <th>P&L</th>
                                <th>Status</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody id="recent-trades-body">
                            <tr><td colspan="6" class="loading">No trades yet...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="open-positions-tab" style="display: none;">
                    <table class="trades-table">
                        <thead>
                            <tr>
                                <th>Trade ID</th>
                                <th>Direction</th>
                                <th>Entry Price</th>
                                <th>Current P&L</th>
                                <th>Target</th>
                                <th>Stop Loss</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody id="open-positions-body">
                            <tr><td colspan="7" class="loading">No open positions...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let updateInterval;
        
        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isRunning = true;
                        updateStatus();
                        startUpdates();
                    }
                });
        }
        
        function stopTrading() {
            fetch('/api/stop_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isRunning = false;
                        updateStatus();
                        stopUpdates();
                    }
                });
        }
        
        function updateStatus() {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');
            
            if (isRunning) {
                indicator.className = 'status-indicator status-running';
                text.textContent = 'Trading Active';
            } else {
                indicator.className = 'status-indicator status-stopped';
                text.textContent = 'Trading Stopped';
            }
        }
        
        function startUpdates() {
            updateInterval = setInterval(updateDashboard, 2000); // Update every 2 seconds
            updateDashboard(); // Initial update
        }
        
        function stopUpdates() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        }
        
        function updateDashboard() {
            fetch('/api/trading_status')
                .then(response => response.json())
                .then(data => {
                    // Update model info
                    document.getElementById('model-id').textContent = data.model_info.model_id.split('_').slice(-2).join('_');
                    document.getElementById('target-profit').textContent = `$${data.model_info.net_profit_target.toLocaleString()}`;
                    document.getElementById('target-winrate').textContent = `${data.model_info.win_rate_target}%`;
                    document.getElementById('target-trades').textContent = data.model_info.trades_per_day_target;
                    document.getElementById('composite-score').textContent = `${data.model_info.composite_score}%`;
                    
                    // Update current price
                    document.getElementById('current-price').textContent = `$${data.current_price.toLocaleString()}`;
                    
                    // Update performance
                    document.getElementById('equity').textContent = `$${data.performance.equity.toLocaleString()}`;
                    
                    const totalPnl = data.performance.total_profit;
                    const pnlElement = document.getElementById('total-pnl');
                    pnlElement.textContent = `$${totalPnl.toLocaleString()}`;
                    pnlElement.className = totalPnl >= 0 ? 'value profit' : 'value loss';
                    
                    document.getElementById('win-rate').textContent = `${data.performance.win_rate}%`;
                    document.getElementById('total-trades').textContent = data.performance.total_trades;
                    document.getElementById('daily-trades').textContent = data.performance.daily_trades;
                    document.getElementById('open-positions').textContent = data.performance.open_positions;
                    
                    // Update recent trades
                    updateRecentTrades(data.recent_trades);
                    
                    // Update running status
                    isRunning = data.is_running;
                    updateStatus();
                });
            
            // Update open positions
            fetch('/api/open_positions')
                .then(response => response.json())
                .then(data => {
                    updateOpenPositions(data);
                });
        }
        
        function updateRecentTrades(trades) {
            const tbody = document.getElementById('recent-trades-body');
            
            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="loading">No trades yet...</td></tr>';
                return;
            }
            
            tbody.innerHTML = trades.map(trade => `
                <tr>
                    <td>${trade.trade_id}</td>
                    <td>${trade.direction}</td>
                    <td>$${trade.entry_price.toLocaleString()}</td>
                    <td class="${trade.pnl >= 0 ? 'profit' : 'loss'}">$${trade.pnl.toLocaleString()}</td>
                    <td>${trade.status.replace('CLOSED_', '')}</td>
                    <td>${trade.timestamp}</td>
                </tr>
            `).join('');
        }
        
        function updateOpenPositions(positions) {
            const tbody = document.getElementById('open-positions-body');
            
            if (positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="loading">No open positions...</td></tr>';
                return;
            }
            
            tbody.innerHTML = positions.map(pos => `
                <tr>
                    <td>${pos.trade_id}</td>
                    <td>${pos.direction}</td>
                    <td>$${pos.entry_price.toLocaleString()}</td>
                    <td class="${pos.current_pnl >= 0 ? 'profit' : 'loss'}">$${pos.current_pnl.toLocaleString()}</td>
                    <td>$${pos.target_profit.toLocaleString()}</td>
                    <td>$${pos.stop_loss.toLocaleString()}</td>
                    <td>${pos.duration}</td>
                </tr>
            `).join('');
        }
        
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide tab content
            document.getElementById('recent-trades-tab').style.display = tabName === 'recent' ? 'block' : 'none';
            document.getElementById('open-positions-tab').style.display = tabName === 'open' ? 'block' : 'none';
        }
        
        // Initialize dashboard
        updateDashboard();
        
        // Auto-refresh every 5 seconds when not actively trading
        setInterval(() => {
            if (!isRunning) {
                updateDashboard();
            }
        }, 5000);
    </script>
</body>
</html>
